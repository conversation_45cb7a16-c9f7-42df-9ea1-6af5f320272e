Great. I’ll outline how to implement an AI agent using Google ADK integrated with Vanna AI to enable natural language chat with both BigQuery and PostgreSQL. This will include:

* A backend in Python using FastAPI that abstracts an AI agent interface
* Two implementations of that interface for BigQuery and PostgreSQL, with best-practice authentication
* A simple chatbot frontend using Next.js
* Instructions for running locally and on Google Cloud Run

I’ll also include steps on setting up service account credentials for BigQuery and access credentials for PostgreSQL.

I’ll get started and will let you know once the full implementation plan is ready.


# AI Agent for Natural Language Database Queries with Google ADK and Vanna AI

## Overview and Architecture

Building a conversational AI agent that can **chat with databases** involves combining a robust agent framework with a specialized NL-to-SQL system. In this guide, we use **Google’s Agent Development Kit (ADK)** and **Vanna AI** to implement an agent that answers natural language questions by querying **BigQuery** or **PostgreSQL** databases. The backend is implemented in Python with FastAPI, and the frontend is a Next.js app providing a chat UI.

**Google ADK** is an open-source framework for developing and orchestrating AI agents, designed to integrate well with Google’s ecosystem (though model-agnostic). It encourages structuring agents with tools and workflows similar to traditional software development. We’ll leverage ADK’s patterns (like tool abstraction for database queries) to design our agent’s logic. **Vanna AI**, on the other hand, is an open-source Retrieval-Augmented Generation (RAG) framework focused on **accurate text-to-SQL generation**. Vanna will handle converting user questions into SQL queries and retrieving results, enabling the “chat with your database” capability.

**System Components**:

* *Backend (FastAPI)*: Hosts the AI agent and a REST API. Defines an abstract database-chat interface and concrete implementations for BigQuery and PostgreSQL. Incorporates Vanna for NL-to-SQL and database querying logic.
* *Frontend (Next.js)*: A simple chat interface that sends user questions to the backend and streams the agent’s responses in real-time.
* *ADK Agent Logic*: The agent uses ADK principles – treating database access as a tool/skill – to decide when to query the database. ADK’s tooling allows the agent to perform actions like database queries as part of its reasoning.
* *Vanna NL-to-SQL Engine*: The agent uses Vanna under the hood to generate SQL from user questions and to execute those queries on the connected database, returning results.

Below, we break down the implementation step-by-step for each part of the system.

## Backend Architecture (Python FastAPI)

### 1. Abstract Agent Interface for Database Chat

We start by defining an abstract base class (or interface) that outlines how our AI agent interacts with databases. This class declares methods for connecting to a database and for processing a natural language question. For example:

```python
from abc import ABC, abstractmethod

class DatabaseChatAgent(ABC):
    @abstractmethod
    def connect(self):
        """Connect to the database (initialize client, auth, etc.)"""
        pass

    @abstractmethod
    def answer_query(self, question: str) -> str:
        """Generate SQL for the question, execute it, and return an answer."""
        pass
```

Concrete implementations for **BigQuery** and **PostgreSQL** will inherit from this and implement the details. This design allows us to swap out or add new database backends easily (e.g. adding another SQL database agent later) without changing the core logic.

### 2. BigQuery Agent Implementation

The BigQuery-backed agent class handles connecting to Google BigQuery and running queries. Key steps include authenticating with Google Cloud, retrieving database schema for context, and using Vanna to generate and execute SQL.

**Authentication & Setup**: We use a Google Cloud **Service Account** for BigQuery access. In your GCP project, create a service account with appropriate BigQuery roles (e.g. BigQuery Data Viewer and/or Job User). Download the service account’s JSON **key file**. For local development, set the environment variable `GOOGLE_APPLICATION_CREDENTIALS` to the path of this JSON key so that Google’s client libraries pick it up. On Google Cloud Run, you typically **don’t need to provide a key file** – if no credentials are set, the code will use the Cloud Run service’s identity via Application Default Credentials. The best practice is to deploy the Cloud Run service with a service account that has BigQuery permissions, rather than embedding keys. (Cloud Run’s default service account can also be used, but you might create a dedicated one and assign it to the service for least privilege.)

**Connecting to BigQuery**: We can use Vanna’s built-in connector to establish the BigQuery connection. For example, using Vanna’s base class method:

```python
vn.connect_to_bigquery(
    project_id="YOUR_GCP_PROJECT_ID",
    cred_file_path="path/to/credentials.json",
)
```

This call will configure Vanna to use BigQuery (optionally using the provided JSON key file if specified). If running on Cloud Run with ADC, you can omit `cred_file_path` – Vanna/Google libraries will use the ambient credentials.

**Schema Ingestion (Training)**: To enable the agent to formulate correct SQL, we provide it with knowledge of the database schema. One approach (used by Vanna) is to query BigQuery’s information schema for tables and columns, then “train” the agent on that metadata. For instance:

```python
# Fetch BigQuery schema information
df_schema = vn.run_sql("SELECT * FROM INFORMATION_SCHEMA.COLUMNS")
# Plan how to chunk schema info for the LLM prompt
plan = vn.get_training_plan_generic(df_schema)
# Optionally, review and refine the plan…
vn.train(plan=plan)  # embed the schema info into the agent's vector store
```

The call to `INFORMATION_SCHEMA.COLUMNS` returns a table of all columns in all tables. Vanna’s `get_training_plan_generic` helps break this into “bite-sized chunks” (pieces of schema info) that can be used by the LLM. By running `vn.train(plan=plan)`, we store these chunks in a vector index so they can be retrieved as context for queries. We might also add extra training data: for example, important business definitions or sample queries. Vanna allows adding **DDL definitions** or **documentation strings** as additional context, which can improve accuracy. This training phase is done once (or whenever the schema changes significantly).

**Query Generation and Execution**: With the schema indexed, the agent can now handle user questions. When a question comes in, the BigQuery agent will:

1. Use Vanna to generate an SQL query that answers the question. Under the hood, Vanna performs a vector similarity search on the stored schema/docs to find relevant pieces (table/column info, etc.) and constructs an LLM prompt. The LLM (e.g. Google Gemini via Vertex AI, or OpenAI GPT-4, depending on configuration) then produces an SQL query. This corresponds to `vn.generate_sql(question)`. (If using Vanna’s high-level API, `vn.ask(question)` can do this plus execution in one step – more on this below.)
2. Execute the SQL on BigQuery. Vanna’s `vn.run_sql(sql_query)` will run the query and return results (often as a pandas DataFrame or list of rows, depending on configuration).
3. Format the results into a user-friendly answer. For numeric or text results, this might be a simple reading of the top rows. If the question expects a chart or visualization, Vanna can even generate Plotly code for a graph, but in a basic Q\&A we can just list the results.

We might implement `answer_query` like:

```python
def answer_query(self, question: str) -> str:
    sql = self.vn.generate_sql(question)        # Step 1: NL -> SQL
    results_df = self.vn.run_sql(sql)           # Step 2: execute SQL on BigQuery
    return format_results(results_df, sql)      # Step 3: format answer
```

Where `format_results` is a helper to convert the DataFrame into a text answer (or to handle cases where the SQL itself fully answers, e.g. a COUNT query result). Optionally, the agent could include the SQL itself in the answer for transparency.

**Note:** Vanna’s `vn.ask()` convenience method actually does both generation and execution internally, plus chart generation if applicable. In a notebook, you might simply do `vn.ask("Question")` and get an answer. However, in a web app context, it’s recommended to call the steps individually. This gives us more control – for example, we can stream the LLM’s output as it’s generating SQL or as it’s composing the final answer, rather than waiting for the entire `ask` to complete. We follow this approach for better real-time response streaming.

**ADK Integration:** Within ADK’s paradigm, the BigQuery access can be thought of as a **Tool** that the agent can invoke during its reasoning. We could integrate this by exposing a function like `query_bigquery(sql: str)` as an ADK `FunctionTool`. The agent’s prompt would be configured so it knows it can use `query_bigquery` to retrieve data. For simplicity, our implementation directly uses Vanna’s methods as described, but this could be wrapped for ADK’s agent loop if desired. Notably, Google ADK provides deep integration with Google Cloud services – in fact, ADK sample agents show “data science agents” that natively use BigQuery for analysis. In our design, Vanna serves as the smart layer on top of BigQuery, and ADK helps orchestrate the overall conversation and tool usage logic.

### 3. PostgreSQL Agent Implementation

The PostgreSQL agent follows a similar pattern with changes for the different database.

**Secure Credentials Storage**: Instead of a GCP service account, we have a traditional DB connection string (host, port, database name, user, password). Avoid hard-coding these. Use environment variables or a secrets manager to supply them. For example, you might have `PGHOST`, `PGDATABASE`, `PGUSER`, `PGPASSWORD`, `PGPORT` set in your environment. Our FastAPI app can load these on startup (using `os.environ` or a config library) and pass them to the PG agent class. In a cloud deployment, you can mount these as secrets.

**Connecting to PostgreSQL**: We can again use Vanna’s helper to connect. Vanna uses `psycopg2` under the hood for Postgres. For instance:

```python
vn.connect_to_postgres(
    host="db.example.com",
    dbname="my_database",
    user="db_user",
    password="S3cr3tPwd!",
    port=5432
)
```

This sets up `vn.run_sql` to execute queries on the given Postgres database. Our `PostgresChatAgent.connect()` method will perform this using the credentials from env variables (never committing passwords to code). Once connected, the process is much like BigQuery’s: gather schema info and train the model.

**Schema Training**: We can query the Postgres information schema similarly: `SELECT * FROM INFORMATION_SCHEMA.COLUMNS` will work on many SQL databases including Postgres. This yields all table/column metadata. We then call `vn.get_training_plan_generic` on that DataFrame and `vn.train(plan)` to embed the schema info. We could also add any known foreign key relationships or business glossary terms via `vn.train(ddl=...)` or `vn.train(documentation="...")` to enrich the model’s understanding. After a one-time training, the Vanna model (with an underlying vector store) “knows” the schema.

**Query Handling**: On each question, the Postgres agent will do the same steps: use Vanna/LLM to generate an SQL query, run it on the Postgres connection, and format the result. For example, if asked *“What are the top 5 products by sales last month?”*, the agent will embed the question, retrieve relevant schema context (e.g. it might pull info about a `sales` table and `products` table from the vector store), and the LLM will output a SQL query. The agent executes it via `vn.run_sql` and gets results to present to the user. The heavy lifting of translating natural language into correct SQL is handled by Vanna’s model.

**Postgres Best Practices**: Ensure that the database user you connect with has read-only permissions if you only need SELECTs (for safety). Use parameterized queries or proper escaping if you ever incorporate user input directly – though in this case, the LLM is generating the queries from scratch. Also, consider performance: if the DB is very large, you might want to impose LIMITs on queries or have the agent summarize results if too many rows.

### 4. Using Vanna AI for NL-to-SQL and Chat Logic

Vanna’s architecture underpins our agent’s ability to understand questions. Under the hood, Vanna is performing a **retrieval-augmented generation**: it stores structured data (like schema definitions or even sample records) in a vector index and pulls relevant bits into the LLM’s context when a question is asked. This dramatically improves the accuracy of SQL generation because the model is “reminded” of exact table and column names, data types, etc.

We largely follow Vanna’s recommended usage: connect to the DB, collect training data, and then use `vn.ask()` or its constituent functions to handle queries. An important design choice is whether to **auto-run** the SQL or just return it. In our agent, we chose to automatically execute the SQL and give the answer, making the experience seamless. (Vanna’s API lets you just get the SQL string if you prefer to verify it or run it conditionally.) In fact, Vanna’s high-level `ask` will by default *return the SQL query and can even auto-run it* – in a Jupyter environment it executes and prints results for you. In our backend, we explicitly call generate and run to have more control.

By using ADK, we could incorporate additional agent behaviors around this core capability. For example, the agent could have a conversation memory (to handle follow-up questions like “Now show me the top 5 for last year” reusing context from the previous query) – ADK provides session context and multi-turn conversation handling. We won’t delve deep into follow-ups here, but the architecture is amenable to extension. If implementing memory, you might store the last queries or results in the agent’s state (ADK context or Vanna’s context) and append them to new prompts as needed.

Finally, because ADK is model-agnostic, you can plug in different LLMs for Vanna to use. Vanna supports OpenAI, Anthropic, Google Gemini, HuggingFace local models, etc.. For instance, you could use OpenAI’s GPT-4 via API by configuring `OpenAIChat` instead of `GoogleGeminiChat` in the Vanna mixin class (as shown in Vanna docs). The rest of the pipeline remains the same. Ensure your LLM of choice is configured to allow function/tool usage if you integrate via ADK’s tool mechanism, or just let Vanna handle prompting internally as we did for simplicity.

## Frontend Implementation (Next.js Chat Interface)

The frontend is a lightweight **Next.js 13** application that provides a chat UI for user interactions. We will create a single-page chat interface where the user can type questions and see responses from the agent (including partial streaming of the answer).

**UI Layout**: The interface consists of a list of message bubbles (user and agent messages) and an input box at the bottom for the user to ask questions. Since Next.js is React-based, we can manage the chat state using React hooks.

**Connecting to the Backend**: We use Next.js’s fetch API or the **Vercel AI SDK** for React which provides a convenient `useChat` hook. This hook manages the message state and handles calling the backend API. In our case, we’ll point it to the FastAPI endpoint (e.g. `/ask`). For example:

```jsx
import { useChat } from 'ai/react';  // from @vercel/ai package

export default function Chat() {
  const { messages, input, handleInputChange, handleSubmit } = useChat({
    api: "/ask"  // assuming same domain or proxied, else full URL like http://localhost:8000/ask
  });

  return (
    <main>
      <div className="chat-window">
        {messages.map(m => (
          <div key={m.id} className={m.role === 'user' ? 'user-msg' : 'agent-msg'}>
            <b>{m.role === 'user' ? "You" : "AI"}:</b> {m.content}
          </div>
        ))}
      </div>
      <form onSubmit={handleSubmit}>
        <input 
          value={input} 
          onChange={handleInputChange} 
          placeholder="Ask a question..." 
        />
        <button type="submit">Send</button>
      </form>
    </main>
  );
}
```

In this snippet, the `useChat` hook handles sending the user’s input to the `/ask` endpoint and streams back the response. The `messages` array contains the conversation history (with each message having a `role` and `content`). We iterate over it to display messages. When the user submits the form, `handleSubmit` sends the request. The hook will append the user message to the list immediately, and then start streaming the AI’s response as it arrives.

Styling (CSS) is omitted here for brevity – one can add basic styles to differentiate user vs. agent messages (e.g., different background colors, alignment, etc.). The focus is on functionality.

**Streaming Responses**: We want the agent’s answer to appear in real-time, character by character or token by token, rather than all at once after a long pause. The above setup with `useChat` assumes the backend uses server-sent events (SSE) to stream. We must implement our FastAPI `/ask` endpoint to support a streaming response.

## Backend: Streaming Responses with FastAPI

To stream responses, FastAPI can return a `StreamingResponse` that yields data periodically. In our `/ask` handler, we will initiate the query-answer process and yield the answer as it’s generated. There are a couple of stages where streaming is useful:

* **While the LLM is generating the SQL query**: If the SQL is complex, streaming its tokens isn’t very useful to the end-user (it might be technical), so we typically don’t stream this step to the UI.
* **While the agent is generating the final answer**: If the agent is formulating a multi-sentence explanation or listing many rows, streaming can be helpful. Often, though, the agent’s “final answer” might just be directly derived from the DB results (which we get nearly instantly once the query executes). The bigger latency is waiting for the LLM to output the SQL and possibly a statement about the results. Therefore, streaming is most noticeable when the LLM is producing a long answer summary or reasoning.

If using OpenAI GPT-4 or similar via Vanna, we can request the openAI API to stream tokens. If using Vertex AI (Gemini), streaming is also supported in their SDK. We then propagate those tokens through FastAPI.

**FastAPI Endpoint Example**:

```python
from fastapi import FastAPI
from fastapi.responses import StreamingResponse

app = FastAPI()

@app.post("/ask")
async def ask_question(question: str):
    async def answer_generator(q: str):
        # 1. Yield a partial response or indicator (optional)
        # 2. Generate SQL via LLM streaming (if supported by Vanna for the model)
        sql_stream = vn.generate_sql_stream(q)  # hypothetical streaming version
        async for partial_sql in sql_stream:
            # (We likely skip showing raw SQL to user; instead, we could log it internally)
            pass
        # 3. Execute the final SQL and get results
        results = vn.run_sql(sql_stream.final_query)
        # 4. Format results – if this is large, yield chunks
        for chunk in format_results_streaming(results):
            yield chunk  # yield piece by piece
    return StreamingResponse(answer_generator(question), media_type="text/event-stream")
```

This pseudocode illustrates the idea: the `StreamingResponse` will iterate over whatever `answer_generator` yields and send it to the client as `text/event-stream`. In practice, one might simplify: since Vanna’s `vn.ask` already returns the answer after running the query, you could call that and stream the final answer text in chunks (e.g., split by sentence). However, a more dynamic approach is to integrate at the LLM level. For example, if using OpenAI API, you’d call the chat completion with `stream=True` and iterate over `response.iter_chunks()` yielding each token or phrase. Each yielded chunk is sent as an SSE to the frontend, which the `useChat` hook then appends to the message content in real-time.

FastAPI requires the response to be an async generator or a normal generator to stream. Ensure to set the correct media type (`text/event-stream`) for SSE. The Next.js `useChat` hook knows how to handle this event stream and update the UI incrementally.

**Important**: If the question could have a very large answer (like thousands of rows), consider pagination or summarization. For example, the agent might decide to answer “I found 10,000 rows, here are the first 10…” rather than dumping all. This kind of logic can be built into the agent’s LLM prompt or post-processing.

For our scope, we assume the queries return reasonably sized results that can be shown directly.

## Deployment Instructions

With the backend and frontend implemented, we need to deploy the system for use both locally (for testing) and on Google Cloud Run (for production).

### Running Locally

**Prerequisites**: Python 3.x for FastAPI, Node.js for Next.js, and access to the databases. Ensure BigQuery credentials and Postgres credentials are set up.

1. **Environment Variables**: Prepare a `.env` or set env vars in your shell for:

   * `GOOGLE_APPLICATION_CREDENTIALS` pointing to your BigQuery service account JSON (if using BigQuery locally) – not needed if you use `cred_file_path` directly in code.
   * `PGHOST`, `PGDATABASE`, `PGUSER`, `PGPASSWORD`, `PGPORT` for Postgres connection.
   * Any API keys or configuration for Vanna’s LLM if required (for example, OpenAI API key if using OpenAI). Vanna can also use its hosted model ID and API key – those would be `VANNA_MODEL` and `VANNA_API_KEY` as seen in Vanna docs if you use Vanna’s hosted vector store. In our case, if we self-host everything, these might not be needed unless using Vanna Cloud.

2. **Start the FastAPI Backend**: Install dependencies: `pip install fastapi uvicorn google-cloud-bigquery vanna psycopg2-binary`. (The `vanna` package includes connectors for many DBs; use extras like `vanna[bigquery]` to get BigQuery support automatically.) Then run the app:

   ```bash
   uvicorn app:app --reload
   ```

   assuming your FastAPI app object is in `app.py`. This will start the server on `http://127.0.0.1:8000` by default. You should see logs if startup was successful (and possibly initial Vanna training loading up).

3. **Start the Next.js Frontend**: In the Next.js project directory, install packages (`npm install` or `yarn`) which should include `next`, `react`, and `@vercel/ai` (for useChat). Update the API endpoint URL if necessary – if FastAPI runs on a different host/port, configure the `useChat` hook accordingly (for development, you might set `api: "http://localhost:8000/ask"` and enable CORS in FastAPI). Start the dev server:

   ```bash
   npm run dev
   ```

   This by default runs on `http://localhost:3000`. Open that in your browser. You should see your chat interface. Try a question like “**How many orders were placed last month?**”. The frontend will send it to FastAPI, FastAPI will log the progress (it may take a few seconds on first query as the LLM runs), and then you should see the answer stream in the UI.

   *If you face CORS issues*: add FastAPI’s `CORSMiddleware` in `app.py`:

   ```python
   from fastapi.middleware.cors import CORSMiddleware
   app.add_middleware(CORSMiddleware, allow_origins=["http://localhost:3000"], allow_methods=["*"], allow_headers=["*"])
   ```

   This permits the Next.js dev server to call the API.

### Deploying to Google Cloud Run

For production, we containerize both backend and frontend. You can deploy them separately (frontend as a static site or Vercel app, and backend on Cloud Run) or even serve both from one service (not typical with Next.js – better to keep them separate).

**FastAPI Backend on Cloud Run**:

1. **Dockerize**: Create a Dockerfile for the FastAPI app. For example:

   ```Dockerfile
   FROM python:3.11-slim
   WORKDIR /app
   COPY requirements.txt . 
   RUN pip install -r requirements.txt
   COPY . .
   ENV PORT 8080
   CMD ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8080"]
   ```

   Include `google-cloud-bigquery`, `vanna`, `fastapi`, etc. in your requirements. Build the image:

   ```bash
   docker build -t gcr.io/<YOUR_PROJECT>/db-chat-backend:v1 .
   ```

   Push to Google Container Registry or Artifact Registry:

   ```bash
   docker push gcr.io/<YOUR_PROJECT>/db-chat-backend:v1
   ```

2. **Deploy**: Use gcloud to deploy the container to Cloud Run:

   ```bash
   gcloud run deploy db-chat-backend \
       --image gcr.io/<YOUR_PROJECT>/db-chat-backend:v1 \
       --platform managed --region <your-region> \
       --allow-unauthenticated \
       --memory 2Gi
   ```

   (Add `--cpu=...` or other flags as needed. Allow unauthenticated if you don’t require auth for the API.)

3. **Set Environment Variables/Secrets**:

   * In Cloud Run console or via gcloud flags, set environment vars for your Postgres credentials (e.g. `PGHOST`, etc.) and any other config (like `VANNA_MODEL` if using one, though in our design we primarily rely on local data).
   * **BigQuery Auth**: As noted, attach a service account to this Cloud Run service that has access to BigQuery. In the Cloud Run deployment settings, under Security, specify the service account. Cloud Run will automatically obtain tokens for that service account. Therefore, you do *not* need to set `GOOGLE_APPLICATION_CREDENTIALS` or manage a key file in Cloud Run. As the docs state, if no env var is set, ADC will use the Cloud Run service’s identity for Google API calls. Just ensure the service account has roles like “BigQuery User/Viewer” as appropriate. This avoids storing a secret key on the server.
   * **Postgres Access**: If your Postgres is hosted (Cloud SQL or elsewhere), you need the Cloud Run service to be able to reach it. For Cloud SQL, you’d use the Cloud SQL Python Connector or Cloud Run’s VPC connector integration. For an external Postgres, you might need to enable outbound networking (Cloud Run allows outbound HTTPS by default, but for direct IP to a DB you might need a VPC connector). This setup depends on where the DB lives. In a simple scenario, if it’s a publicly accessible database, ensure to whitelist Cloud Run’s IP range or use a secure connection string.

4. **Testing**: Once deployed, you’ll have a URL for the backend (like `https://db-chat-backend-<hash>.a.run.app`). You can test the `/ask` endpoint via curl or Postman to ensure it’s responding (you should get SSE text stream).

**Next.js Frontend Deployment**:

* Easiest path: Deploy on **Vercel**. Next.js apps deploy seamlessly to Vercel’s platform. You’d set the environment variable on Vercel for the backend API URL (if different). For example, an env var `NEXT_PUBLIC_API_URL = https://db-chat-backend-<hash>.a.run.app`. Then in your `useChat` or fetch calls, use that URL. Deploying on Vercel will give you a nice domain and global CDN. If you prefer to use Cloud Run for the frontend as well, you can. You’d build a Docker image FROM node:18-alpine, build the Next app (or use `next export` for a static export if no server-side rendering is needed) and serve it with a simple Node HTTP server or `next start`. However, this is more work; using Vercel or Netlify for the front might be quicker.
* If using Vercel, just be mindful of CORS – your backend URL should be allowed. You can restrict allowed origins to your Vercel domain in the FastAPI CORS config for better security.

**Cloud Run and Secrets**: One more note on storing sensitive data – Cloud Run can directly mount secrets from **Secret Manager** as env vars or files. For instance, you could store the Postgres password in Secret Manager and then in Cloud Run console, map that secret to the `PGPASSWORD` environment variable. This way it’s not stored in plaintext in Cloud Run config. Similarly, if you did choose to use a service account key (not recommended on Cloud Run), you could put that JSON in Secret Manager and mount it, then have `GOOGLE_APPLICATION_CREDENTIALS` point to the mounted file path. But again, using the service account binding is safer and easier for GCP services.

After deploying both components, you should have a live URL for the frontend where users can chat. The messages will be sent to the Cloud Run backend, which will interact with BigQuery and/or Postgres and stream back answers. You now have an AI-powered data analyst bot: ask it in English, and it will crunch database data to answer.

## References and Relevant Resources

* **Google ADK Documentation & Samples** – Official docs for the Agent Development Kit and a collection of sample agents on GitHub. These illustrate patterns for tool use (e.g., calling external APIs or databases) and deploying agents.
* **Vanna AI GitHub Repository** – Source code of Vanna, which provides the base classes and methods we used for NL->SQL generation. The Vanna documentation covers connecting to various databases like BigQuery and Postgres (e.g. usage of `vn.connect_to_bigquery` and `vn.connect_to_postgres`), as well as how to train on schema and ask questions.
* **Stack Overflow: Cloud Run Service Account** – Explanation that Cloud Run uses its default or specified service account for ADC if no credentials file is provided. This informed our approach to BigQuery auth on Cloud Run.
* **Next.js Streaming Guide** – Vercel’s AI SDK and `useChat` hook usage (see code example above) and FastAPI streaming response pattern were used to implement the real-time chat experience.

By combining ADK’s structured agent approach with Vanna’s SQL generation and the deployment on scalable cloud services, you achieve a robust system where end-users can ask natural questions and get answers sourced from live database data. This architecture can be extended to other databases or even multiple databases at once (the agent could decide which data source to use based on the question). The modular design (separating DB-specific logic and using an abstract interface) makes such extensions feasible. Happy querying!
