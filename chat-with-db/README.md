# Chat with DB AI Agent

An AI agent for natural language database queries using Google ADK and Vanna AI.

## Features

- Natural language to SQL conversion
- Support for BigQuery and PostgreSQL databases
- Streaming responses for real-time feedback
- FastAPI backend with easy-to-use API

## Prerequisites

- Python 3.8+
- PostgreSQL or BigQuery database
- OpenAI API key or Google API key

## Installation

1. Clone the repository
2. Install dependencies:

```bash
pip install -r requirements.txt
```

3. Create a `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

4. Edit the `.env` file with your database credentials and API keys.

## Running the Server

```bash
python server.py
```

The server will start on http://localhost:8000 by default.

## API Endpoints

- `GET /`: Root endpoint with API information
- `GET /health`: Health check endpoint
- `POST /api/chat/ask`: Ask a question to the database
- `POST /api/chat/ask/stream`: Ask a question and stream the response
- `POST /api/chat/generate-sql`: Generate SQL for a question
- `POST /api/chat/execute-sql`: Execute SQL on the database

## Example Usage

```python
import requests

# Ask a question
response = requests.post(
    "http://localhost:8000/api/chat/ask",
    json={
        "question": "How many users registered last month?",
        "db_type": "postgres"
    }
)
print(response.json())
```

## Docker Deployment

Build and run the Docker container:

```bash
docker build -t chat-with-db .
docker run -p 8000:8080 --env-file .env chat-with-db
```

## Environment Variables

- `PORT`: Server port (default: 8000)
- `OPENAI_API_KEY`: OpenAI API key
- `GOOGLE_API_KEY`: Google API key
- `GOOGLE_CLOUD_PROJECT`: Google Cloud project ID
- `GOOGLE_APPLICATION_CREDENTIALS`: Path to Google Cloud credentials JSON file
- `PGHOST`: PostgreSQL host
- `PGDATABASE`: PostgreSQL database name
- `PGUSER`: PostgreSQL username
- `PGPASSWORD`: PostgreSQL password
- `PGPORT`: PostgreSQL port (default: 5432)
