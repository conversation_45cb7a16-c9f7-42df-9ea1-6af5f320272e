import os
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from dotenv import load_dotenv

from .routers import chat_router

# Load environment variables from .env file
load_dotenv()

# Create FastAPI app
app = FastAPI(
    title="Chat with DB AI Agent",
    description="AI agent for natural language database queries using Google ADK and Vanna AI",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # For development; restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(chat_router)


@app.get("/")
async def root():
    """
    Root endpoint
    
    Returns:
        dict: Basic information about the API
    """
    return {
        "message": "Chat with DB AI Agent API",
        "docs_url": "/docs",
        "redoc_url": "/redoc",
    }


@app.get("/health")
async def health_check():
    """
    Health check endpoint
    
    Returns:
        dict: Health status
    """
    return {"status": "healthy"}


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """
    Global exception handler
    
    Args:
        request: The request that caused the exception
        exc: The exception
        
    Returns:
        JSONResponse: Error response
    """
    return JSONResponse(
        status_code=500,
        content={"detail": str(exc)},
    )
