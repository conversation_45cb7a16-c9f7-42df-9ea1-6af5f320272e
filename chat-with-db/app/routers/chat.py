import json
from typing import Dict, Any, Optional, List
from fastapi import APIRout<PERSON>, Depends, HTTPException, Request, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from sse_starlette.sse import EventSourceResponse
import asyncio

from ..agents import DatabaseChatAgent, AgentFactory


# Define request and response models
class ChatRequest(BaseModel):
    question: str
    db_type: str = "postgres"  # Default to postgres
    stream: bool = False


class ChatResponse(BaseModel):
    question: str
    sql: str
    results: Dict[str, Any]
    explanation: str


# Create router
router = APIRouter(
    prefix="/api/chat",
    tags=["chat"],
    responses={404: {"description": "Not found"}},
)


# Dependency to get the appropriate database agent
async def get_db_agent(db_type: str = "postgres") -> DatabaseChatAgent:
    """
    Get the appropriate database agent based on the specified type
    
    Args:
        db_type: Type of database ('bigquery' or 'postgres')
        
    Returns:
        DatabaseChatAgent: An instance of the appropriate database chat agent
    """
    try:
        # Create agent from environment variables
        agent = AgentFactory.create_agent_from_env(db_type)
        
        # Connect to the database
        if not agent.connect():
            raise HTTPException(status_code=500, detail=f"Failed to connect to {db_type} database")
        
        # Train the agent on the database schema
        if not agent.train():
            raise HTTPException(status_code=500, detail=f"Failed to train agent on {db_type} schema")
        
        return agent
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/ask", response_model=ChatResponse)
async def ask_question(
    request: ChatRequest,
    agent: DatabaseChatAgent = Depends(get_db_agent)
):
    """
    Ask a question to the database chat agent
    
    Args:
        request: Chat request containing the question and database type
        agent: Database chat agent (injected by dependency)
        
    Returns:
        ChatResponse: The answer to the question
    """
    if request.stream:
        return EventSourceResponse(agent.answer_query_stream(request.question))
    else:
        response = await agent.answer_query(request.question)
        if "error" in response:
            raise HTTPException(status_code=500, detail=response["error"])
        return response


@router.post("/ask/stream")
async def ask_question_stream(
    request: ChatRequest,
    agent: DatabaseChatAgent = Depends(get_db_agent)
):
    """
    Ask a question to the database chat agent and stream the response
    
    Args:
        request: Chat request containing the question and database type
        agent: Database chat agent (injected by dependency)
        
    Returns:
        StreamingResponse: Streaming response with the answer
    """
    return EventSourceResponse(agent.answer_query_stream(request.question))


@router.post("/generate-sql")
async def generate_sql(
    request: ChatRequest,
    agent: DatabaseChatAgent = Depends(get_db_agent)
):
    """
    Generate SQL for a natural language question
    
    Args:
        request: Chat request containing the question and database type
        agent: Database chat agent (injected by dependency)
        
    Returns:
        Dict[str, str]: The generated SQL
    """
    sql = await agent.generate_sql(request.question)
    return {"sql": sql}


@router.post("/execute-sql")
async def execute_sql(
    sql: str,
    db_type: str = "postgres",
    agent: DatabaseChatAgent = Depends(get_db_agent)
):
    """
    Execute SQL on the database
    
    Args:
        sql: SQL query to execute
        db_type: Type of database ('bigquery' or 'postgres')
        agent: Database chat agent (injected by dependency)
        
    Returns:
        Dict[str, Any]: The query results
    """
    results = await agent.execute_sql(sql)
    return results
