import os
from typing import Dict, Any, Optional
from .base_agent import DatabaseChatAgent
from .bigquery_agent import BigQueryChatAgent
from .postgres_agent import PostgresChatAgent


class AgentFactory:
    """
    Factory class for creating database chat agents
    """

    @staticmethod
    def create_agent(
        db_type: str,
        config: Dict[str, Any],
        model: str = "openai/gpt-4",
        api_key: Optional[str] = None
    ) -> DatabaseChatAgent:
        """
        Create a database chat agent based on the specified type and configuration

        Args:
            db_type: Type of database ('bigquery' or 'postgres')
            config: Configuration parameters for the database connection
            model: LLM model to use for SQL generation
            api_key: API key for the LLM model

        Returns:
            DatabaseChatAgent: An instance of the appropriate database chat agent
        """
        if db_type.lower() == "bigquery":
            return BigQueryChatAgent(
                project_id=config.get("project_id"),
                cred_file_path=config.get("cred_file_path"),
                model=model,
                api_key=api_key
            )
        elif db_type.lower() == "postgres":
            return PostgresChatAgent(
                host=config.get("host"),
                dbname=config.get("dbname"),
                user=config.get("user"),
                password=config.get("password"),
                vanna_model_name=config.get("vanna_model_name", "default-model"),
                vanna_api_key=config.get("vanna_api_key", api_key),
                port=config.get("port", 5432)
            )
        else:
            raise ValueError(f"Unsupported database type: {db_type}")

    @staticmethod
    def create_agent_from_env(
        db_type: str,
        model: str = "openai/gpt-4",
        api_key: Optional[str] = None
    ) -> DatabaseChatAgent:
        """
        Create a database chat agent using environment variables for configuration

        Args:
            db_type: Type of database ('bigquery' or 'postgres')
            model: LLM model to use for SQL generation
            api_key: API key for the LLM model

        Returns:
            DatabaseChatAgent: An instance of the appropriate database chat agent
        """
        if db_type.lower() == "bigquery":
            config = {
                "project_id": os.environ.get("GOOGLE_CLOUD_PROJECT"),
                "cred_file_path": os.environ.get("GOOGLE_APPLICATION_CREDENTIALS")
            }
            return BigQueryChatAgent(
                project_id=config["project_id"],
                cred_file_path=config["cred_file_path"],
                model=model,
                api_key=api_key or os.environ.get("OPENAI_API_KEY") or os.environ.get("GOOGLE_API_KEY")
            )
        elif db_type.lower() == "postgres":
            config = {
                "host": os.environ.get("PGHOST"),
                "dbname": os.environ.get("PGDATABASE"),
                "user": os.environ.get("PGUSER"),
                "password": os.environ.get("PGPASSWORD"),
                "port": int(os.environ.get("PGPORT", "5432"))
            }
            return PostgresChatAgent(
                host=config["host"],
                dbname=config["dbname"],
                user=config["user"],
                password=config["password"],
                vanna_model_name=os.environ.get("VANNA_MODEL", "default-model"),
                vanna_api_key=os.environ.get("VANNA_API_KEY", api_key or os.environ.get("OPENAI_API_KEY") or os.environ.get("GOOGLE_API_KEY")),
                port=config["port"]
            )
        else:
            raise ValueError(f"Unsupported database type: {db_type}")
