from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional


class DatabaseChatAgent(ABC):
    """
    Abstract base class for database chat agents.
    Defines the interface for agents that can connect to a database
    and answer natural language queries by generating SQL.
    """
    
    @abstractmethod
    def connect(self) -> bool:
        """
        Connect to the database (initialize client, auth, etc.)
        
        Returns:
            bool: True if connection was successful, False otherwise
        """
        pass
    
    @abstractmethod
    def train(self, additional_context: Optional[str] = None) -> bool:
        """
        Train the agent on the database schema and optionally additional context
        
        Args:
            additional_context: Optional additional documentation or context to train on
            
        Returns:
            bool: True if training was successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def generate_sql(self, question: str) -> str:
        """
        Generate SQL for the given natural language question
        
        Args:
            question: The natural language question to convert to SQL
            
        Returns:
            str: The generated SQL query
        """
        pass
    
    @abstractmethod
    async def execute_sql(self, sql: str) -> Dict[str, Any]:
        """
        Execute the given SQL query on the database
        
        Args:
            sql: The SQL query to execute
            
        Returns:
            Dict[str, Any]: The query results
        """
        pass
    
    @abstractmethod
    async def answer_query(self, question: str) -> Dict[str, Any]:
        """
        Generate SQL for the question, execute it, and return an answer
        
        Args:
            question: The natural language question to answer
            
        Returns:
            Dict[str, Any]: The answer with SQL, results, and explanation
        """
        pass
    
    @abstractmethod
    async def answer_query_stream(self, question: str):
        """
        Stream the answer to a question
        
        Args:
            question: The natural language question to answer
            
        Yields:
            str: Chunks of the answer as they are generated
        """
        pass
