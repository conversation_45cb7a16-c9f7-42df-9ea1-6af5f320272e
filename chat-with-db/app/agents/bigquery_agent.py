import os
import json
import pandas as pd
from typing import Dict, Any, List, Optional, AsyncGenerator
import vanna as vn
from google.cloud import bigquery
from .base_agent import DatabaseChatAgent


class BigQueryChatAgent(DatabaseChatAgent):
    """
    Implementation of DatabaseChatAgent for Google BigQuery
    """
    
    def __init__(
        self,
        project_id: str,
        cred_file_path: Optional[str] = None,
        model: str = "openai/gpt-4",
        api_key: Optional[str] = None,
    ):
        """
        Initialize the BigQuery chat agent
        
        Args:
            project_id: Google Cloud project ID
            cred_file_path: Path to Google Cloud credentials JSON file (optional)
            model: LLM model to use for SQL generation
            api_key: API key for the LLM model
        """
        self.project_id = project_id
        self.cred_file_path = cred_file_path
        self.client = None
        self.is_connected = False
        self.is_trained = False
        
        # Initialize Vanna with the specified model
        if model.startswith("openai"):
            from vanna.openai import OpenAI
            self.vn = OpenAI(model=model.split("/")[1], api_key=api_key)
        elif model.startswith("google"):
            from vanna.google import GoogleGemini
            self.vn = GoogleGemini(model=model.split("/")[1], api_key=api_key)
        else:
            raise ValueError(f"Unsupported model: {model}")
    
    def connect(self) -> bool:
        """
        Connect to BigQuery using the provided credentials
        
        Returns:
            bool: True if connection was successful, False otherwise
        """
        try:
            # Connect to BigQuery
            if self.cred_file_path:
                self.vn.connect_to_bigquery(
                    project_id=self.project_id,
                    cred_file_path=self.cred_file_path
                )
            else:
                # Use Application Default Credentials
                self.vn.connect_to_bigquery(project_id=self.project_id)
            
            # Test connection by running a simple query
            self.vn.run_sql("SELECT 1")
            self.is_connected = True
            return True
        except Exception as e:
            print(f"Error connecting to BigQuery: {e}")
            self.is_connected = False
            return False
    
    def train(self, additional_context: Optional[str] = None) -> bool:
        """
        Train the agent on the BigQuery schema
        
        Args:
            additional_context: Optional additional documentation or context to train on
            
        Returns:
            bool: True if training was successful, False otherwise
        """
        if not self.is_connected:
            print("Not connected to BigQuery. Call connect() first.")
            return False
        
        try:
            # Fetch schema information from BigQuery
            df_schema = self.vn.run_sql("SELECT * FROM INFORMATION_SCHEMA.COLUMNS")
            
            # Generate training plan from schema
            plan = self.vn.get_training_plan_generic(df_schema)
            
            # Train the model on the schema
            self.vn.train(plan=plan)
            
            # Add additional context if provided
            if additional_context:
                self.vn.train(documentation=additional_context)
            
            self.is_trained = True
            return True
        except Exception as e:
            print(f"Error training on BigQuery schema: {e}")
            return False
    
    async def generate_sql(self, question: str) -> str:
        """
        Generate SQL for the given natural language question
        
        Args:
            question: The natural language question to convert to SQL
            
        Returns:
            str: The generated SQL query
        """
        if not self.is_trained:
            raise ValueError("Agent not trained. Call train() first.")
        
        try:
            sql = self.vn.generate_sql(question)
            return sql
        except Exception as e:
            print(f"Error generating SQL: {e}")
            return f"Error generating SQL: {str(e)}"
    
    async def execute_sql(self, sql: str) -> Dict[str, Any]:
        """
        Execute the given SQL query on BigQuery
        
        Args:
            sql: The SQL query to execute
            
        Returns:
            Dict[str, Any]: The query results
        """
        if not self.is_connected:
            raise ValueError("Not connected to BigQuery. Call connect() first.")
        
        try:
            results_df = self.vn.run_sql(sql)
            
            # Convert DataFrame to dict for JSON serialization
            if isinstance(results_df, pd.DataFrame):
                results = results_df.to_dict(orient="records")
                columns = list(results_df.columns)
            else:
                results = [{"result": results_df}]
                columns = ["result"]
                
            return {
                "results": results,
                "columns": columns,
                "row_count": len(results) if isinstance(results, list) else 1
            }
        except Exception as e:
            print(f"Error executing SQL: {e}")
            return {"error": str(e)}
    
    async def answer_query(self, question: str) -> Dict[str, Any]:
        """
        Generate SQL for the question, execute it, and return an answer
        
        Args:
            question: The natural language question to answer
            
        Returns:
            Dict[str, Any]: The answer with SQL, results, and explanation
        """
        if not self.is_trained:
            return {"error": "Agent not trained. Call train() first."}
        
        try:
            # Generate SQL
            sql = await self.generate_sql(question)
            
            # Execute SQL
            results = await self.execute_sql(sql)
            
            # Generate explanation
            explanation = self.vn.generate_explanation(question, sql, results)
            
            return {
                "question": question,
                "sql": sql,
                "results": results,
                "explanation": explanation
            }
        except Exception as e:
            print(f"Error answering query: {e}")
            return {"error": str(e)}
    
    async def answer_query_stream(self, question: str) -> AsyncGenerator[str, None]:
        """
        Stream the answer to a question
        
        Args:
            question: The natural language question to answer
            
        Yields:
            str: Chunks of the answer as they are generated
        """
        if not self.is_trained:
            yield json.dumps({"error": "Agent not trained. Call train() first."})
            return
        
        try:
            # First yield a message that we're generating SQL
            yield json.dumps({"status": "generating_sql"})
            
            # Generate SQL
            sql = await self.generate_sql(question)
            yield json.dumps({"sql": sql})
            
            # Execute SQL
            yield json.dumps({"status": "executing_sql"})
            results = await self.execute_sql(sql)
            yield json.dumps({"results": results})
            
            # Generate explanation
            yield json.dumps({"status": "generating_explanation"})
            explanation = self.vn.generate_explanation(question, sql, results)
            
            # Yield the explanation in chunks for a streaming effect
            for i in range(0, len(explanation), 10):
                chunk = explanation[i:i+10]
                yield json.dumps({"explanation_chunk": chunk})
            
            # Final complete response
            yield json.dumps({
                "complete": True,
                "question": question,
                "sql": sql,
                "results": results,
                "explanation": explanation
            })
        except Exception as e:
            print(f"Error in streaming answer: {e}")
            yield json.dumps({"error": str(e)})
