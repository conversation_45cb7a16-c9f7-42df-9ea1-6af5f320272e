import os
import json
import pandas as pd
import psycopg2
from datetime import datetime, date
from decimal import Decimal
from typing import Dict, Any, List, Optional, AsyncGenerator

from .base_agent import DatabaseChatAgent


def serialize_value(value):
    """Convert non-JSON serializable values to JSON serializable ones"""
    if isinstance(value, (datetime, date)):
        return value.isoformat()
    elif isinstance(value, Decimal):
        return float(value)
    elif value is None:
        return None
    else:
        return str(value)


class PostgresChatAgent(DatabaseChatAgent):
    """
    Simple implementation of DatabaseChatAgent for PostgreSQL without Vanna
    This is a basic implementation that can be extended with proper NL-to-SQL capabilities
    """

    def __init__(
        self,
        host: str,
        dbname: str,
        user: str,
        password: str,
        vanna_model_name: str = "default",
        vanna_api_key: str = "default",
        port: int = 5432,
    ):
        """
        Initialize the PostgreSQL chat agent

        Args:
            host: PostgreSQL host
            dbname: Database name
            user: Database user
            password: Database password
            vanna_model_name: Not used in this simple implementation
            vanna_api_key: Not used in this simple implementation
            port: Database port (default: 5432)
        """
        self.host = host
        self.dbname = dbname
        self.user = user
        self.password = password
        self.port = port
        self.is_connected = False
        self.is_trained = False
        self.connection = None
        self.schema_info = None

    def connect(self) -> bool:
        """
        Connect to PostgreSQL using the provided credentials

        Returns:
            bool: True if connection was successful, False otherwise
        """
        try:
            # Connect to PostgreSQL
            self.connection = psycopg2.connect(
                host=self.host,
                database=self.dbname,
                user=self.user,
                password=self.password,
                port=self.port
            )

            # Test connection by running a simple query
            cursor = self.connection.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()

            self.is_connected = True
            return True
        except Exception as e:
            print(f"Error connecting to PostgreSQL: {e}")
            self.is_connected = False
            return False

    def train(self, additional_context: Optional[str] = None) -> bool:
        """
        Train the agent on the PostgreSQL schema

        Args:
            additional_context: Optional additional documentation or context to train on

        Returns:
            bool: True if training was successful, False otherwise
        """
        if not self.is_connected:
            print("Not connected to PostgreSQL. Call connect() first.")
            return False

        try:
            # Fetch schema information from PostgreSQL
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT
                    table_schema,
                    table_name,
                    column_name,
                    data_type,
                    is_nullable
                FROM
                    information_schema.columns
                WHERE
                    table_schema NOT IN ('pg_catalog', 'information_schema')
            """)

            schema_rows = cursor.fetchall()
            cursor.close()

            # Store schema information
            self.schema_info = {
                'tables': {},
                'columns': schema_rows
            }

            # Group by table
            for row in schema_rows:
                table_schema, table_name, column_name, data_type, is_nullable = row
                table_key = f"{table_schema}.{table_name}"
                if table_key not in self.schema_info['tables']:
                    self.schema_info['tables'][table_key] = []
                self.schema_info['tables'][table_key].append({
                    'column': column_name,
                    'type': data_type,
                    'nullable': is_nullable
                })

            self.is_trained = True
            return True
        except Exception as e:
            print(f"Error training on PostgreSQL schema: {e}")
            return False

    async def generate_sql(self, question: str) -> str:
        """
        Generate SQL for the given natural language question
        This implementation analyzes the question and generates appropriate SQL

        Args:
            question: The natural language question to convert to SQL

        Returns:
            str: The generated SQL query
        """
        if not self.is_trained:
            raise ValueError("Agent not trained. Call train() first.")

        # Analyze the question and generate appropriate SQL
        question_lower = question.lower()

        # Schema-related queries
        if "table" in question_lower or "schema" in question_lower:
            return """
                SELECT table_name, column_name, data_type
                FROM information_schema.columns
                WHERE table_schema NOT IN ('pg_catalog', 'information_schema')
                ORDER BY table_name, ordinal_position
                LIMIT 20
            """

        # Count queries - analyze what to count
        elif "count" in question_lower or "how many" in question_lower:
            return self._generate_count_query(question_lower)

        # List/show queries
        elif any(word in question_lower for word in ["list", "show", "display", "get"]):
            return self._generate_list_query(question_lower)

        # Default query - try to find relevant table
        else:
            return self._generate_default_query(question_lower)

    def _generate_count_query(self, question_lower: str) -> str:
        """Generate COUNT queries based on the question"""
        # Look for table names in the question
        if self.schema_info and self.schema_info['tables']:
            table_names = list(self.schema_info['tables'].keys())

            # Check for specific table mentions
            for table_key in table_names:
                table_name = table_key.split('.')[-1]  # Get table name without schema

                # Check various forms of table names
                if (table_name in question_lower or
                    table_name.replace('_', ' ') in question_lower or
                    table_name.replace('_', '') in question_lower):
                    return f"SELECT COUNT(*) as total_{table_name} FROM {table_key}"

                # Check for plural/singular variations
                if table_name.endswith('s') and table_name[:-1] in question_lower:
                    return f"SELECT COUNT(*) as total_{table_name} FROM {table_key}"
                elif not table_name.endswith('s') and f"{table_name}s" in question_lower:
                    return f"SELECT COUNT(*) as total_{table_name} FROM {table_key}"

            # Check for common entity names
            if "compan" in question_lower:
                for table_key in table_names:
                    if "company" in table_key:
                        return f"SELECT COUNT(*) as total_companies FROM {table_key}"

            if "user" in question_lower:
                for table_key in table_names:
                    if "user" in table_key:
                        return f"SELECT COUNT(*) as total_users FROM {table_key}"

            if "task" in question_lower:
                for table_key in table_names:
                    if "task" in table_key:
                        return f"SELECT COUNT(*) as total_tasks FROM {table_key}"

            if "role" in question_lower:
                for table_key in table_names:
                    if "role" in table_key:
                        return f"SELECT COUNT(*) as total_roles FROM {table_key}"

            # Default to first table if no specific match
            first_table = table_names[0]
            return f"SELECT COUNT(*) as total_records FROM {first_table}"

        return "SELECT 1 as demo_count"

    def _generate_list_query(self, question_lower: str) -> str:
        """Generate SELECT queries to list/show data"""
        if self.schema_info and self.schema_info['tables']:
            table_names = list(self.schema_info['tables'].keys())

            # Look for specific table mentions
            for table_key in table_names:
                table_name = table_key.split('.')[-1]

                if (table_name in question_lower or
                    table_name.replace('_', ' ') in question_lower):
                    return f"SELECT * FROM {table_key} LIMIT 10"

            # Check for common entity names
            if "compan" in question_lower:
                for table_key in table_names:
                    if "company" in table_key and not "user" in table_key and not "role" in table_key:
                        return f"SELECT * FROM {table_key} LIMIT 10"

            if "user" in question_lower:
                for table_key in table_names:
                    if "user" in table_key:
                        return f"SELECT * FROM {table_key} LIMIT 10"

            # Default to first table
            first_table = table_names[0]
            return f"SELECT * FROM {first_table} LIMIT 5"

        return "SELECT 'No tables found' as message"

    def _generate_default_query(self, question_lower: str) -> str:
        """Generate default queries for unrecognized patterns"""
        if self.schema_info and self.schema_info['tables']:
            table_names = list(self.schema_info['tables'].keys())

            # Try to find relevant table based on keywords
            for table_key in table_names:
                table_name = table_key.split('.')[-1]
                if table_name in question_lower:
                    return f"SELECT * FROM {table_key} LIMIT 5"

            # Default to showing available tables
            return """
                SELECT table_name,
                       COUNT(*) as column_count
                FROM information_schema.columns
                WHERE table_schema NOT IN ('pg_catalog', 'information_schema')
                GROUP BY table_name
                ORDER BY table_name
            """

        return "SELECT 'Hello from PostgreSQL!' as message"

    async def execute_sql(self, sql: str) -> Dict[str, Any]:
        """
        Execute the given SQL query on PostgreSQL

        Args:
            sql: The SQL query to execute

        Returns:
            Dict[str, Any]: The query results
        """
        if not self.is_connected:
            raise ValueError("Not connected to PostgreSQL. Call connect() first.")

        try:
            cursor = self.connection.cursor()
            cursor.execute(sql)

            # Fetch results
            rows = cursor.fetchall()

            # Get column names
            columns = [desc[0] for desc in cursor.description] if cursor.description else []

            cursor.close()

            # Convert to list of dictionaries with proper serialization
            results = []
            for row in rows:
                result_dict = {}
                for i, value in enumerate(row):
                    if i < len(columns):
                        result_dict[columns[i]] = serialize_value(value)
                results.append(result_dict)

            return {
                "results": results,
                "columns": columns,
                "row_count": len(results)
            }
        except Exception as e:
            print(f"Error executing SQL: {e}")
            return {"error": str(e)}

    async def answer_query(self, question: str) -> Dict[str, Any]:
        """
        Generate SQL for the question, execute it, and return an answer

        Args:
            question: The natural language question to answer

        Returns:
            Dict[str, Any]: The answer with SQL, results, and explanation
        """
        if not self.is_trained:
            return {"error": "Agent not trained. Call train() first."}

        try:
            # Generate SQL
            sql = await self.generate_sql(question)

            # Execute SQL
            results = await self.execute_sql(sql)

            # Generate simple explanation
            if "error" in results:
                explanation = f"There was an error executing the query: {results['error']}"
            else:
                row_count = results.get('row_count', 0)
                explanation = f"I executed the SQL query and found {row_count} result(s). The query searched the database based on your question: '{question}'"

            return {
                "question": question,
                "sql": sql,
                "results": results,
                "explanation": explanation
            }
        except Exception as e:
            print(f"Error answering query: {e}")
            return {"error": str(e)}

    async def answer_query_stream(self, question: str) -> AsyncGenerator[str, None]:
        """
        Stream the answer to a question

        Args:
            question: The natural language question to answer

        Yields:
            str: Chunks of the answer as they are generated
        """
        if not self.is_trained:
            yield json.dumps({"error": "Agent not trained. Call train() first."})
            return

        try:
            # First yield a message that we're generating SQL
            yield json.dumps({"status": "generating_sql"})

            # Generate SQL
            sql = await self.generate_sql(question)
            yield json.dumps({"sql": sql})

            # Execute SQL
            yield json.dumps({"status": "executing_sql"})
            results = await self.execute_sql(sql)
            yield json.dumps({"results": results})

            # Generate explanation
            yield json.dumps({"status": "generating_explanation"})

            # Generate simple explanation
            if "error" in results:
                explanation = f"There was an error executing the query: {results['error']}"
            else:
                row_count = results.get('row_count', 0)
                explanation = f"I executed the SQL query and found {row_count} result(s). The query searched the database based on your question: '{question}'"

            # Yield the explanation in chunks for a streaming effect
            for i in range(0, len(explanation), 10):
                chunk = explanation[i:i+10]
                yield json.dumps({"explanation_chunk": chunk})

            # Final complete response
            yield json.dumps({
                "complete": True,
                "question": question,
                "sql": sql,
                "results": results,
                "explanation": explanation
            })
        except Exception as e:
            print(f"Error in streaming answer: {e}")
            yield json.dumps({"error": str(e)})