Metadata-Version: 2.4
Name: vanna
Version: 0.7.9
Summary: Generate SQL queries from natural language
Author-email: <PERSON><PERSON> <<EMAIL>>
Requires-Python: >=3.9
Description-Content-Type: text/markdown
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
License-File: LICENSE
Requires-Dist: requests
Requires-Dist: tabulate
Requires-Dist: plotly
Requires-Dist: pandas
Requires-Dist: sqlparse
Requires-Dist: kaleido
Requires-Dist: flask
Requires-Dist: flask-sock
Requires-Dist: flasgger
Requires-Dist: sqlalchemy
Requires-Dist: psycopg2-binary ; extra == "all"
Requires-Dist: db-dtypes ; extra == "all"
Requires-Dist: PyMySQL ; extra == "all"
Requires-Dist: google-cloud-bigquery ; extra == "all"
Requires-Dist: snowflake-connector-python ; extra == "all"
Requires-Dist: duckdb ; extra == "all"
Requires-Dist: openai ; extra == "all"
Requires-Dist: qianfan ; extra == "all"
Requires-Dist: mistralai>=1.0.0 ; extra == "all"
Requires-Dist: chromadb<1.0.0 ; extra == "all"
Requires-Dist: anthropic ; extra == "all"
Requires-Dist: zhipuai ; extra == "all"
Requires-Dist: marqo ; extra == "all"
Requires-Dist: google-generativeai ; extra == "all"
Requires-Dist: google-cloud-aiplatform ; extra == "all"
Requires-Dist: qdrant-client ; extra == "all"
Requires-Dist: fastembed ; extra == "all"
Requires-Dist: ollama ; extra == "all"
Requires-Dist: httpx ; extra == "all"
Requires-Dist: opensearch-py ; extra == "all"
Requires-Dist: opensearch-dsl ; extra == "all"
Requires-Dist: transformers ; extra == "all"
Requires-Dist: pinecone ; extra == "all"
Requires-Dist: pymilvus[model] ; extra == "all"
Requires-Dist: weaviate-client ; extra == "all"
Requires-Dist: azure-search-documents ; extra == "all"
Requires-Dist: azure-identity ; extra == "all"
Requires-Dist: azure-common ; extra == "all"
Requires-Dist: faiss-cpu ; extra == "all"
Requires-Dist: boto ; extra == "all"
Requires-Dist: boto3 ; extra == "all"
Requires-Dist: botocore ; extra == "all"
Requires-Dist: langchain_core ; extra == "all"
Requires-Dist: langchain_postgres ; extra == "all"
Requires-Dist: langchain-community ; extra == "all"
Requires-Dist: langchain-huggingface ; extra == "all"
Requires-Dist: xinference-client ; extra == "all"
Requires-Dist: anthropic ; extra == "anthropic"
Requires-Dist: azure-search-documents ; extra == "azuresearch"
Requires-Dist: azure-identity ; extra == "azuresearch"
Requires-Dist: azure-common ; extra == "azuresearch"
Requires-Dist: fastembed ; extra == "azuresearch"
Requires-Dist: boto3 ; extra == "bedrock"
Requires-Dist: botocore ; extra == "bedrock"
Requires-Dist: google-cloud-bigquery ; extra == "bigquery"
Requires-Dist: chromadb<1.0.0 ; extra == "chromadb"
Requires-Dist: clickhouse_connect ; extra == "clickhouse"
Requires-Dist: duckdb ; extra == "duckdb"
Requires-Dist: faiss-cpu ; extra == "faiss-cpu"
Requires-Dist: faiss-gpu ; extra == "faiss-gpu"
Requires-Dist: google-generativeai ; extra == "gemini"
Requires-Dist: google-generativeai ; extra == "google"
Requires-Dist: google-cloud-aiplatform ; extra == "google"
Requires-Dist: transformers ; extra == "hf"
Requires-Dist: marqo ; extra == "marqo"
Requires-Dist: pymilvus[model] ; extra == "milvus"
Requires-Dist: mistralai>=1.0.0 ; extra == "mistralai"
Requires-Dist: PyMySQL ; extra == "mysql"
Requires-Dist: ollama ; extra == "ollama"
Requires-Dist: httpx ; extra == "ollama"
Requires-Dist: openai ; extra == "openai"
Requires-Dist: opensearch-py ; extra == "opensearch"
Requires-Dist: opensearch-dsl ; extra == "opensearch"
Requires-Dist: langchain-community ; extra == "opensearch"
Requires-Dist: langchain-huggingface ; extra == "opensearch"
Requires-Dist: oracledb ; extra == "oracle"
Requires-Dist: chromadb<1.0.0 ; extra == "oracle"
Requires-Dist: langchain-postgres>=0.0.12 ; extra == "pgvector"
Requires-Dist: pinecone ; extra == "pinecone"
Requires-Dist: fastembed ; extra == "pinecone"
Requires-Dist: psycopg2-binary ; extra == "postgres"
Requires-Dist: db-dtypes ; extra == "postgres"
Requires-Dist: qdrant-client ; extra == "qdrant"
Requires-Dist: fastembed ; extra == "qdrant"
Requires-Dist: qianfan ; extra == "qianfan"
Requires-Dist: snowflake-connector-python ; extra == "snowflake"
Requires-Dist: tox ; extra == "test"
Requires-Dist: vllm ; extra == "vllm"
Requires-Dist: weaviate-client ; extra == "weaviate"
Requires-Dist: xinference-client ; extra == "xinference-client"
Requires-Dist: zhipuai ; extra == "zhipuai"
Project-URL: Bug Tracker, https://github.com/vanna-ai/vanna/issues
Project-URL: Homepage, https://github.com/vanna-ai/vanna
Provides-Extra: all
Provides-Extra: anthropic
Provides-Extra: azuresearch
Provides-Extra: bedrock
Provides-Extra: bigquery
Provides-Extra: chromadb
Provides-Extra: clickhouse
Provides-Extra: duckdb
Provides-Extra: faiss-cpu
Provides-Extra: faiss-gpu
Provides-Extra: gemini
Provides-Extra: google
Provides-Extra: hf
Provides-Extra: marqo
Provides-Extra: milvus
Provides-Extra: mistralai
Provides-Extra: mysql
Provides-Extra: ollama
Provides-Extra: openai
Provides-Extra: opensearch
Provides-Extra: oracle
Provides-Extra: pgvector
Provides-Extra: pinecone
Provides-Extra: postgres
Provides-Extra: qdrant
Provides-Extra: qianfan
Provides-Extra: snowflake
Provides-Extra: test
Provides-Extra: vllm
Provides-Extra: weaviate
Provides-Extra: xinference-client
Provides-Extra: zhipuai



| GitHub | PyPI | Documentation | Gurubase |
| ------ | ---- | ------------- | -------- |
| [![GitHub](https://img.shields.io/badge/GitHub-vanna-blue?logo=github)](https://github.com/vanna-ai/vanna) | [![PyPI](https://img.shields.io/pypi/v/vanna?logo=pypi)](https://pypi.org/project/vanna/) | [![Documentation](https://img.shields.io/badge/Documentation-vanna-blue?logo=read-the-docs)](https://vanna.ai/docs/) | [![Gurubase](https://img.shields.io/badge/Gurubase-Ask%20Vanna%20Guru-006BFF)](https://gurubase.io/g/vanna) |

# Vanna
Vanna is an MIT-licensed open-source Python RAG (Retrieval-Augmented Generation) framework for SQL generation and related functionality.

https://github.com/vanna-ai/vanna/assets/7146154/1901f47a-515d-4982-af50-f12761a3b2ce

![vanna-quadrants](https://github.com/vanna-ai/vanna/assets/7146154/1c7c88ba-c144-4ecf-a028-cf5ba7344ca2)

## How Vanna works

![Screen Recording 2024-01-24 at 11 21 37 AM](https://github.com/vanna-ai/vanna/assets/7146154/1d2718ad-12a8-4a76-afa2-c61754462f93)


Vanna works in two easy steps - train a RAG "model" on your data, and then ask questions which will return SQL queries that can be set up to automatically run on your database.

1. **Train a RAG "model" on your data**.
2. **Ask questions**.

![](img/vanna-readme-diagram.png)

If you don't know what RAG is, don't worry -- you don't need to know how this works under the hood to use it. You just need to know that you "train" a model, which stores some metadata and then use it to "ask" questions.

See the [base class](https://github.com/vanna-ai/vanna/blob/main/src/vanna/base/base.py) for more details on how this works under the hood.

## User Interfaces
These are some of the user interfaces that we've built using Vanna. You can use these as-is or as a starting point for your own custom interface.

- [Jupyter Notebook](https://vanna.ai/docs/postgres-openai-vanna-vannadb/)
- [vanna-ai/vanna-streamlit](https://github.com/vanna-ai/vanna-streamlit)
- [vanna-ai/vanna-flask](https://github.com/vanna-ai/vanna-flask)
- [vanna-ai/vanna-slack](https://github.com/vanna-ai/vanna-slack)

## Supported LLMs

- [OpenAI](https://github.com/vanna-ai/vanna/tree/main/src/vanna/openai)
- [Anthropic](https://github.com/vanna-ai/vanna/tree/main/src/vanna/anthropic)
- [Gemini](https://github.com/vanna-ai/vanna/blob/main/src/vanna/google/gemini_chat.py)
- [HuggingFace](https://github.com/vanna-ai/vanna/blob/main/src/vanna/hf/hf.py)
- [AWS Bedrock](https://github.com/vanna-ai/vanna/tree/main/src/vanna/bedrock)
- [Ollama](https://github.com/vanna-ai/vanna/tree/main/src/vanna/ollama)
- [Qianwen](https://github.com/vanna-ai/vanna/tree/main/src/vanna/qianwen)
- [Qianfan](https://github.com/vanna-ai/vanna/tree/main/src/vanna/qianfan)
- [Zhipu](https://github.com/vanna-ai/vanna/tree/main/src/vanna/ZhipuAI)

## Supported VectorStores

- [AzureSearch](https://github.com/vanna-ai/vanna/tree/main/src/vanna/azuresearch)
- [Opensearch](https://github.com/vanna-ai/vanna/tree/main/src/vanna/opensearch)
- [PgVector](https://github.com/vanna-ai/vanna/tree/main/src/vanna/pgvector)
- [PineCone](https://github.com/vanna-ai/vanna/tree/main/src/vanna/pinecone)
- [ChromaDB](https://github.com/vanna-ai/vanna/tree/main/src/vanna/chromadb)
- [FAISS](https://github.com/vanna-ai/vanna/tree/main/src/vanna/faiss)
- [Marqo](https://github.com/vanna-ai/vanna/tree/main/src/vanna/marqo)
- [Milvus](https://github.com/vanna-ai/vanna/tree/main/src/vanna/milvus)
- [Qdrant](https://github.com/vanna-ai/vanna/tree/main/src/vanna/qdrant)
- [Weaviate](https://github.com/vanna-ai/vanna/tree/main/src/vanna/weaviate)
- [Oracle](https://github.com/vanna-ai/vanna/tree/main/src/vanna/oracle)

## Supported Databases

- [PostgreSQL](https://www.postgresql.org/)
- [MySQL](https://www.mysql.com/)
- [PrestoDB](https://prestodb.io/)
- [Apache Hive](https://hive.apache.org/)
- [ClickHouse](https://clickhouse.com/)
- [Snowflake](https://www.snowflake.com/en/)
- [Oracle](https://www.oracle.com/)
- [Microsoft SQL Server](https://www.microsoft.com/en-us/sql-server/sql-server-downloads)
- [BigQuery](https://cloud.google.com/bigquery)
- [SQLite](https://www.sqlite.org/)
- [DuckDB](https://duckdb.org/)


## Getting started
See the [documentation](https://vanna.ai/docs/) for specifics on your desired database, LLM, etc.

If you want to get a feel for how it works after training, you can try this [Colab notebook](https://vanna.ai/docs/app/).


### Install
```bash
pip install vanna
```

There are a number of optional packages that can be installed so see the [documentation](https://vanna.ai/docs/) for more details.

### Import
See the [documentation](https://vanna.ai/docs/) if you're customizing the LLM or vector database.

```python
# The import statement will vary depending on your LLM and vector database. This is an example for OpenAI + ChromaDB

from vanna.openai.openai_chat import OpenAI_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore

class MyVanna(ChromaDB_VectorStore, OpenAI_Chat):
    def __init__(self, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        OpenAI_Chat.__init__(self, config=config)

vn = MyVanna(config={'api_key': 'sk-...', 'model': 'gpt-4-...'})

# See the documentation for other options

```


## Training
You may or may not need to run these `vn.train` commands depending on your use case. See the [documentation](https://vanna.ai/docs/) for more details.

These statements are shown to give you a feel for how it works.

### Train with DDL Statements
DDL statements contain information about the table names, columns, data types, and relationships in your database.

```python
vn.train(ddl="""
    CREATE TABLE IF NOT EXISTS my-table (
        id INT PRIMARY KEY,
        name VARCHAR(100),
        age INT
    )
""")
```

### Train with Documentation
Sometimes you may want to add documentation about your business terminology or definitions.

```python
vn.train(documentation="Our business defines XYZ as ...")
```

### Train with SQL
You can also add SQL queries to your training data. This is useful if you have some queries already laying around. You can just copy and paste those from your editor to begin generating new SQL.

```python
vn.train(sql="SELECT name, age FROM my-table WHERE name = 'John Doe'")
```


## Asking questions
```python
vn.ask("What are the top 10 customers by sales?")
```

You'll get SQL
```sql
SELECT c.c_name as customer_name,
        sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales
FROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o
        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c
        ON o.o_custkey = c.c_custkey
GROUP BY customer_name
ORDER BY total_sales desc limit 10;
```

If you've connected to a database, you'll get the table:
<div>
<table border="1" class="dataframe">
  <thead>
    <tr style="text-align: right;">
      <th></th>
      <th>CUSTOMER_NAME</th>
      <th>TOTAL_SALES</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <th>0</th>
      <td>Customer#000143500</td>
      <td>6757566.0218</td>
    </tr>
    <tr>
      <th>1</th>
      <td>Customer#000095257</td>
      <td>6294115.3340</td>
    </tr>
    <tr>
      <th>2</th>
      <td>Customer#000087115</td>
      <td>6184649.5176</td>
    </tr>
    <tr>
      <th>3</th>
      <td>Customer#000131113</td>
      <td>6080943.8305</td>
    </tr>
    <tr>
      <th>4</th>
      <td>Customer#000134380</td>
      <td>6075141.9635</td>
    </tr>
    <tr>
      <th>5</th>
      <td>Customer#000103834</td>
      <td>6059770.3232</td>
    </tr>
    <tr>
      <th>6</th>
      <td>Customer#000069682</td>
      <td>6057779.0348</td>
    </tr>
    <tr>
      <th>7</th>
      <td>Customer#000102022</td>
      <td>6039653.6335</td>
    </tr>
    <tr>
      <th>8</th>
      <td>Customer#000098587</td>
      <td>6027021.5855</td>
    </tr>
    <tr>
      <th>9</th>
      <td>Customer#000064660</td>
      <td>5905659.6159</td>
    </tr>
  </tbody>
</table>
</div>

You'll also get an automated Plotly chart:
![](img/top-10-customers.png)

## RAG vs. Fine-Tuning
RAG
- Portable across LLMs
- Easy to remove training data if any of it becomes obsolete
- Much cheaper to run than fine-tuning
- More future-proof -- if a better LLM comes out, you can just swap it out

Fine-Tuning
- Good if you need to minimize tokens in the prompt
- Slow to get started
- Expensive to train and run (generally)

## Why Vanna?

1. **High accuracy on complex datasets.**
    - Vanna’s capabilities are tied to the training data you give it
    - More training data means better accuracy for large and complex datasets
2. **Secure and private.**
    - Your database contents are never sent to the LLM or the vector database
    - SQL execution happens in your local environment
3. **Self learning.**
    - If using via Jupyter, you can choose to "auto-train" it on the queries that were successfully executed
    - If using via other interfaces, you can have the interface prompt the user to provide feedback on the results
    - Correct question to SQL pairs are stored for future reference and make the future results more accurate
4. **Supports any SQL database.**
    - The package allows you to connect to any SQL database that you can otherwise connect to with Python
5. **Choose your front end.**
    - Most people start in a Jupyter Notebook.
    - Expose to your end users via Slackbot, web app, Streamlit app, or a custom front end.

## Extending Vanna
Vanna is designed to connect to any database, LLM, and vector database. There's a [VannaBase](https://github.com/vanna-ai/vanna/blob/main/src/vanna/base/base.py) abstract base class that defines some basic functionality. The package provides implementations for use with OpenAI and ChromaDB. You can easily extend Vanna to use your own LLM or vector database. See the [documentation](https://vanna.ai/docs/) for more details.

## Vanna in 100 Seconds

https://github.com/vanna-ai/vanna/assets/7146154/eb90ee1e-aa05-4740-891a-4fc10e611cab

## More resources
 - [Full Documentation](https://vanna.ai/docs/)
 - [Website](https://vanna.ai)
 - [Discord group for support](https://discord.gg/qUZYKHremx)

