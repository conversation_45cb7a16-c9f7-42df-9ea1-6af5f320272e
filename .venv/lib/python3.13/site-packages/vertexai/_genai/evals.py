# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Code generated by the Google Gen AI SDK generator DO NOT EDIT.

import logging
from typing import Any, Optional, Union
from urllib.parse import urlencode

from google.genai import _api_module
from google.genai import _common
from google.genai import types as genai_types
from google.genai._api_client import BaseApiClient
from google.genai._common import get_value_by_path as getv
from google.genai._common import set_value_by_path as setv

from . import types

logger = logging.getLogger("google_genai.evals")


def _BleuInstance_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["prediction"]) is not None:
        setv(to_object, ["prediction"], getv(from_object, ["prediction"]))

    if getv(from_object, ["reference"]) is not None:
        setv(to_object, ["reference"], getv(from_object, ["reference"]))

    return to_object


def _BleuSpec_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["use_effective_order"]) is not None:
        setv(
            to_object,
            ["useEffectiveOrder"],
            getv(from_object, ["use_effective_order"]),
        )

    return to_object


def _BleuInput_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instances"]) is not None:
        setv(
            to_object,
            ["instances"],
            [
                _BleuInstance_to_vertex(api_client, item, to_object)
                for item in getv(from_object, ["instances"])
            ],
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _BleuSpec_to_vertex(
                api_client, getv(from_object, ["metric_spec"]), to_object
            ),
        )

    return to_object


def _ExactMatchInstance_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["prediction"]) is not None:
        setv(to_object, ["prediction"], getv(from_object, ["prediction"]))

    if getv(from_object, ["reference"]) is not None:
        setv(to_object, ["reference"], getv(from_object, ["reference"]))

    return to_object


def _ExactMatchSpec_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    return to_object


def _ExactMatchInput_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instances"]) is not None:
        setv(
            to_object,
            ["instances"],
            [
                _ExactMatchInstance_to_vertex(api_client, item, to_object)
                for item in getv(from_object, ["instances"])
            ],
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _ExactMatchSpec_to_vertex(
                api_client, getv(from_object, ["metric_spec"]), to_object
            ),
        )

    return to_object


def _RougeInstance_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["prediction"]) is not None:
        setv(to_object, ["prediction"], getv(from_object, ["prediction"]))

    if getv(from_object, ["reference"]) is not None:
        setv(to_object, ["reference"], getv(from_object, ["reference"]))

    return to_object


def _RougeSpec_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["rouge_type"]) is not None:
        setv(to_object, ["rougeType"], getv(from_object, ["rouge_type"]))

    if getv(from_object, ["split_summaries"]) is not None:
        setv(
            to_object,
            ["splitSummaries"],
            getv(from_object, ["split_summaries"]),
        )

    if getv(from_object, ["use_stemmer"]) is not None:
        setv(to_object, ["useStemmer"], getv(from_object, ["use_stemmer"]))

    return to_object


def _RougeInput_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instances"]) is not None:
        setv(
            to_object,
            ["instances"],
            [
                _RougeInstance_to_vertex(api_client, item, to_object)
                for item in getv(from_object, ["instances"])
            ],
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _RougeSpec_to_vertex(
                api_client, getv(from_object, ["metric_spec"]), to_object
            ),
        )

    return to_object


def _PointwiseMetricInstance_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["json_instance"]) is not None:
        setv(to_object, ["jsonInstance"], getv(from_object, ["json_instance"]))

    return to_object


def _PointwiseMetricSpec_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["metric_prompt_template"]) is not None:
        setv(
            to_object,
            ["metricPromptTemplate"],
            getv(from_object, ["metric_prompt_template"]),
        )

    return to_object


def _PointwiseMetricInput_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instance"]) is not None:
        setv(
            to_object,
            ["instance"],
            _PointwiseMetricInstance_to_vertex(
                api_client, getv(from_object, ["instance"]), to_object
            ),
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _PointwiseMetricSpec_to_vertex(
                api_client, getv(from_object, ["metric_spec"]), to_object
            ),
        )

    return to_object


def _PairwiseMetricInstance_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["json_instance"]) is not None:
        setv(to_object, ["jsonInstance"], getv(from_object, ["json_instance"]))

    return to_object


def _PairwiseMetricSpec_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["metric_prompt_template"]) is not None:
        setv(
            to_object,
            ["metricPromptTemplate"],
            getv(from_object, ["metric_prompt_template"]),
        )

    return to_object


def _PairwiseMetricInput_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instance"]) is not None:
        setv(
            to_object,
            ["instance"],
            _PairwiseMetricInstance_to_vertex(
                api_client, getv(from_object, ["instance"]), to_object
            ),
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _PairwiseMetricSpec_to_vertex(
                api_client, getv(from_object, ["metric_spec"]), to_object
            ),
        )

    return to_object


def _ToolCallValidInstance_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["prediction"]) is not None:
        setv(to_object, ["prediction"], getv(from_object, ["prediction"]))

    if getv(from_object, ["reference"]) is not None:
        setv(to_object, ["reference"], getv(from_object, ["reference"]))

    return to_object


def _ToolCallValidSpec_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    return to_object


def _ToolCallValidInput_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instances"]) is not None:
        setv(
            to_object,
            ["instances"],
            [
                _ToolCallValidInstance_to_vertex(api_client, item, to_object)
                for item in getv(from_object, ["instances"])
            ],
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _ToolCallValidSpec_to_vertex(
                api_client, getv(from_object, ["metric_spec"]), to_object
            ),
        )

    return to_object


def _ToolNameMatchInstance_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["prediction"]) is not None:
        setv(to_object, ["prediction"], getv(from_object, ["prediction"]))

    if getv(from_object, ["reference"]) is not None:
        setv(to_object, ["reference"], getv(from_object, ["reference"]))

    return to_object


def _ToolNameMatchSpec_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    return to_object


def _ToolNameMatchInput_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instances"]) is not None:
        setv(
            to_object,
            ["instances"],
            [
                _ToolNameMatchInstance_to_vertex(api_client, item, to_object)
                for item in getv(from_object, ["instances"])
            ],
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _ToolNameMatchSpec_to_vertex(
                api_client, getv(from_object, ["metric_spec"]), to_object
            ),
        )

    return to_object


def _ToolParameterKeyMatchInstance_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["prediction"]) is not None:
        setv(to_object, ["prediction"], getv(from_object, ["prediction"]))

    if getv(from_object, ["reference"]) is not None:
        setv(to_object, ["reference"], getv(from_object, ["reference"]))

    return to_object


def _ToolParameterKeyMatchSpec_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    return to_object


def _ToolParameterKeyMatchInput_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instances"]) is not None:
        setv(
            to_object,
            ["instances"],
            [
                _ToolParameterKeyMatchInstance_to_vertex(api_client, item, to_object)
                for item in getv(from_object, ["instances"])
            ],
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _ToolParameterKeyMatchSpec_to_vertex(
                api_client, getv(from_object, ["metric_spec"]), to_object
            ),
        )

    return to_object


def _ToolParameterKVMatchInstance_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["prediction"]) is not None:
        setv(to_object, ["prediction"], getv(from_object, ["prediction"]))

    if getv(from_object, ["reference"]) is not None:
        setv(to_object, ["reference"], getv(from_object, ["reference"]))

    return to_object


def _ToolParameterKVMatchSpec_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["use_strict_string_match"]) is not None:
        setv(
            to_object,
            ["useStrictStringMatch"],
            getv(from_object, ["use_strict_string_match"]),
        )

    return to_object


def _ToolParameterKVMatchInput_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["instances"]) is not None:
        setv(
            to_object,
            ["instances"],
            [
                _ToolParameterKVMatchInstance_to_vertex(api_client, item, to_object)
                for item in getv(from_object, ["instances"])
            ],
        )

    if getv(from_object, ["metric_spec"]) is not None:
        setv(
            to_object,
            ["metricSpec"],
            _ToolParameterKVMatchSpec_to_vertex(
                api_client, getv(from_object, ["metric_spec"]), to_object
            ),
        )

    return to_object


def _EvaluateInstancesRequestParameters_to_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}
    if getv(from_object, ["bleu_input"]) is not None:
        setv(
            to_object,
            ["bleuInput"],
            _BleuInput_to_vertex(
                api_client, getv(from_object, ["bleu_input"]), to_object
            ),
        )

    if getv(from_object, ["exact_match_input"]) is not None:
        setv(
            to_object,
            ["exactMatchInput"],
            _ExactMatchInput_to_vertex(
                api_client, getv(from_object, ["exact_match_input"]), to_object
            ),
        )

    if getv(from_object, ["rouge_input"]) is not None:
        setv(
            to_object,
            ["rougeInput"],
            _RougeInput_to_vertex(
                api_client, getv(from_object, ["rouge_input"]), to_object
            ),
        )

    if getv(from_object, ["pointwise_metric_input"]) is not None:
        setv(
            to_object,
            ["pointwiseMetricInput"],
            _PointwiseMetricInput_to_vertex(
                api_client,
                getv(from_object, ["pointwise_metric_input"]),
                to_object,
            ),
        )

    if getv(from_object, ["pairwise_metric_input"]) is not None:
        setv(
            to_object,
            ["pairwiseMetricInput"],
            _PairwiseMetricInput_to_vertex(
                api_client,
                getv(from_object, ["pairwise_metric_input"]),
                to_object,
            ),
        )

    if getv(from_object, ["tool_call_valid_input"]) is not None:
        setv(
            to_object,
            ["toolCallValidInput"],
            _ToolCallValidInput_to_vertex(
                api_client,
                getv(from_object, ["tool_call_valid_input"]),
                to_object,
            ),
        )

    if getv(from_object, ["tool_name_match_input"]) is not None:
        setv(
            to_object,
            ["toolNameMatchInput"],
            _ToolNameMatchInput_to_vertex(
                api_client,
                getv(from_object, ["tool_name_match_input"]),
                to_object,
            ),
        )

    if getv(from_object, ["tool_parameter_key_match_input"]) is not None:
        setv(
            to_object,
            ["toolParameterKeyMatchInput"],
            _ToolParameterKeyMatchInput_to_vertex(
                api_client,
                getv(from_object, ["tool_parameter_key_match_input"]),
                to_object,
            ),
        )

    if getv(from_object, ["tool_parameter_kv_match_input"]) is not None:
        setv(
            to_object,
            ["toolParameterKvMatchInput"],
            _ToolParameterKVMatchInput_to_vertex(
                api_client,
                getv(from_object, ["tool_parameter_kv_match_input"]),
                to_object,
            ),
        )

    if getv(from_object, ["config"]) is not None:
        setv(to_object, ["config"], getv(from_object, ["config"]))

    return to_object


def _EvaluateInstancesResponse_from_vertex(
    api_client: BaseApiClient,
    from_object: Union[dict[str, Any], object],
    parent_object: Optional[dict[str, Any]] = None,
) -> dict[str, Any]:
    to_object: dict[str, Any] = {}

    if getv(from_object, ["bleuResults"]) is not None:
        setv(to_object, ["bleu_results"], getv(from_object, ["bleuResults"]))

    if getv(from_object, ["exactMatchResults"]) is not None:
        setv(
            to_object,
            ["exact_match_results"],
            getv(from_object, ["exactMatchResults"]),
        )

    if getv(from_object, ["pairwiseMetricResult"]) is not None:
        setv(
            to_object,
            ["pairwise_metric_result"],
            getv(from_object, ["pairwiseMetricResult"]),
        )

    if getv(from_object, ["pointwiseMetricResult"]) is not None:
        setv(
            to_object,
            ["pointwise_metric_result"],
            getv(from_object, ["pointwiseMetricResult"]),
        )

    if getv(from_object, ["rougeResults"]) is not None:
        setv(to_object, ["rouge_results"], getv(from_object, ["rougeResults"]))

    if getv(from_object, ["summarizationVerbosityResult"]) is not None:
        setv(
            to_object,
            ["summarization_verbosity_result"],
            getv(from_object, ["summarizationVerbosityResult"]),
        )

    if getv(from_object, ["toolCallValidResults"]) is not None:
        setv(
            to_object,
            ["tool_call_valid_results"],
            getv(from_object, ["toolCallValidResults"]),
        )

    if getv(from_object, ["toolNameMatchResults"]) is not None:
        setv(
            to_object,
            ["tool_name_match_results"],
            getv(from_object, ["toolNameMatchResults"]),
        )

    if getv(from_object, ["toolParameterKeyMatchResults"]) is not None:
        setv(
            to_object,
            ["tool_parameter_key_match_results"],
            getv(from_object, ["toolParameterKeyMatchResults"]),
        )

    if getv(from_object, ["toolParameterKvMatchResults"]) is not None:
        setv(
            to_object,
            ["tool_parameter_kv_match_results"],
            getv(from_object, ["toolParameterKvMatchResults"]),
        )

    return to_object


class Evals(_api_module.BaseModule):
    def _evaluate_instances(
        self,
        *,
        bleu_input: Optional[types.BleuInputOrDict] = None,
        exact_match_input: Optional[types.ExactMatchInputOrDict] = None,
        rouge_input: Optional[types.RougeInputOrDict] = None,
        pointwise_metric_input: Optional[types.PointwiseMetricInputOrDict] = None,
        pairwise_metric_input: Optional[types.PairwiseMetricInputOrDict] = None,
        tool_call_valid_input: Optional[types.ToolCallValidInputOrDict] = None,
        tool_name_match_input: Optional[types.ToolNameMatchInputOrDict] = None,
        tool_parameter_key_match_input: Optional[
            types.ToolParameterKeyMatchInputOrDict
        ] = None,
        tool_parameter_kv_match_input: Optional[
            types.ToolParameterKVMatchInputOrDict
        ] = None,
        config: Optional[types.EvaluateInstancesConfigOrDict] = None,
    ) -> types.EvaluateInstancesResponse:
        """Evaluates instances based on a given metric."""

        parameter_model = types._EvaluateInstancesRequestParameters(
            bleu_input=bleu_input,
            exact_match_input=exact_match_input,
            rouge_input=rouge_input,
            pointwise_metric_input=pointwise_metric_input,
            pairwise_metric_input=pairwise_metric_input,
            tool_call_valid_input=tool_call_valid_input,
            tool_name_match_input=tool_name_match_input,
            tool_parameter_key_match_input=tool_parameter_key_match_input,
            tool_parameter_kv_match_input=tool_parameter_kv_match_input,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _EvaluateInstancesRequestParameters_to_vertex(
                self._api_client, parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = ":evaluateInstances".format_map(request_url_dict)
            else:
                path = ":evaluateInstances"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[genai_types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response_dict = self._api_client.request(
            "post", path, request_dict, http_options
        )

        if self._api_client.vertexai:
            response_dict = _EvaluateInstancesResponse_from_vertex(
                self._api_client, response_dict
            )

        return_value = types.EvaluateInstancesResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )
        self._api_client._verify_response(return_value)
        return return_value

    def run(self) -> types.EvaluateInstancesResponse:
        """Evaluates an instance of a model.

        This should eventually call _evaluate_instances()
        """
        raise NotImplementedError()

    def evaluate_instances(
        self,
        *,
        metric_config: types._EvaluateInstancesRequestParameters,
    ) -> types.EvaluateInstancesResponse:
        """Evaluates an instance of a model."""

        if isinstance(metric_config, types._EvaluateInstancesRequestParameters):
            metric_config = metric_config.model_dump()
        else:
            metric_config = dict(metric_config)

        return self._evaluate_instances(
            **metric_config,
        )


class AsyncEvals(_api_module.BaseModule):
    async def _evaluate_instances(
        self,
        *,
        bleu_input: Optional[types.BleuInputOrDict] = None,
        exact_match_input: Optional[types.ExactMatchInputOrDict] = None,
        rouge_input: Optional[types.RougeInputOrDict] = None,
        pointwise_metric_input: Optional[types.PointwiseMetricInputOrDict] = None,
        pairwise_metric_input: Optional[types.PairwiseMetricInputOrDict] = None,
        tool_call_valid_input: Optional[types.ToolCallValidInputOrDict] = None,
        tool_name_match_input: Optional[types.ToolNameMatchInputOrDict] = None,
        tool_parameter_key_match_input: Optional[
            types.ToolParameterKeyMatchInputOrDict
        ] = None,
        tool_parameter_kv_match_input: Optional[
            types.ToolParameterKVMatchInputOrDict
        ] = None,
        config: Optional[types.EvaluateInstancesConfigOrDict] = None,
    ) -> types.EvaluateInstancesResponse:
        """Evaluates instances based on a given metric."""

        parameter_model = types._EvaluateInstancesRequestParameters(
            bleu_input=bleu_input,
            exact_match_input=exact_match_input,
            rouge_input=rouge_input,
            pointwise_metric_input=pointwise_metric_input,
            pairwise_metric_input=pairwise_metric_input,
            tool_call_valid_input=tool_call_valid_input,
            tool_name_match_input=tool_name_match_input,
            tool_parameter_key_match_input=tool_parameter_key_match_input,
            tool_parameter_kv_match_input=tool_parameter_kv_match_input,
            config=config,
        )

        request_url_dict: Optional[dict[str, str]]
        if not self._api_client.vertexai:
            raise ValueError("This method is only supported in the Vertex AI client.")
        else:
            request_dict = _EvaluateInstancesRequestParameters_to_vertex(
                self._api_client, parameter_model
            )
            request_url_dict = request_dict.get("_url")
            if request_url_dict:
                path = ":evaluateInstances".format_map(request_url_dict)
            else:
                path = ":evaluateInstances"

        query_params = request_dict.get("_query")
        if query_params:
            path = f"{path}?{urlencode(query_params)}"
        # TODO: remove the hack that pops config.
        request_dict.pop("config", None)

        http_options: Optional[genai_types.HttpOptions] = None
        if (
            parameter_model.config is not None
            and parameter_model.config.http_options is not None
        ):
            http_options = parameter_model.config.http_options

        request_dict = _common.convert_to_dict(request_dict)
        request_dict = _common.encode_unserializable_types(request_dict)

        response_dict = await self._api_client.async_request(
            "post", path, request_dict, http_options
        )

        if self._api_client.vertexai:
            response_dict = _EvaluateInstancesResponse_from_vertex(
                self._api_client, response_dict
            )

        return_value = types.EvaluateInstancesResponse._from_response(
            response=response_dict, kwargs=parameter_model.model_dump()
        )
        self._api_client._verify_response(return_value)
        return return_value
