# -*- coding: utf-8 -*-

# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

from vertexai.preview.evaluation import constants
from vertexai.preview.evaluation.metrics import _base


class TrajectorySingleToolUse(
    _base._AutomaticMetric
):  # pylint: disable=protected-access
    """The TrajectorySingleToolUse Metric.

    Evaluates if a tool is present in the trajectory or not.
    """

    _metric_name = constants.Metric.TRAJECTORY_SINGLE_TOOL_USE

    def __init__(
        self,
        tool_name: str,
    ):
        """Initializes the TrajectorySingleToolUse metric.

        Args:
          tool_name: name of the tool to check.
        """
        self._tool_name = tool_name

        super().__init__(
            metric=TrajectorySingleToolUse._metric_name,
        )

    @property
    def tool_name(self) -> str:
        return self._tool_name
