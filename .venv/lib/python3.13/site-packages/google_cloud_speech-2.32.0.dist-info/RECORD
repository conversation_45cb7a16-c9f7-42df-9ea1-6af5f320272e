google/cloud/speech/__init__.py,sha256=aA6UhjQbidVIWyhFEsUbFEPVBtqWuWWT8rEXxxjNnw4,3274
google/cloud/speech/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech/__pycache__/gapic_version.cpython-313.pyc,,
google/cloud/speech/gapic_version.py,sha256=QTvlFHjTgfvGqO3KkeO-JcBtV0PKK-Cza7sVU2h7_70,653
google/cloud/speech/py.typed,sha256=9XnjN9OQ5mzBvUTS4LvkASo3060SLCUBOLJepHrwVDU,80
google/cloud/speech_v1/__init__.py,sha256=4eCu-WAiL3ZlDYLOS7QGRISeMuBzYG9n2naX4y4sC1c,3182
google/cloud/speech_v1/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v1/__pycache__/gapic_version.cpython-313.pyc,,
google/cloud/speech_v1/__pycache__/helpers.cpython-313.pyc,,
google/cloud/speech_v1/gapic_metadata.json,sha256=a-l-L2sMQfzpzutluqBp36Hj9_jpJ4i6xZTKXbOeHe4,5975
google/cloud/speech_v1/gapic_version.py,sha256=QTvlFHjTgfvGqO3KkeO-JcBtV0PKK-Cza7sVU2h7_70,653
google/cloud/speech_v1/helpers.py,sha256=zg0hbqgPZPAVA2RGnPYpaUJNbL4ywt5UEvzLzX1tTJw,4114
google/cloud/speech_v1/py.typed,sha256=9XnjN9OQ5mzBvUTS4LvkASo3060SLCUBOLJepHrwVDU,80
google/cloud/speech_v1/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/speech_v1/services/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v1/services/adaptation/__init__.py,sha256=mc6LsNyncWccRfmKtQfcxrCI6W-4T8Kyir1Pto1c0LY,753
google/cloud/speech_v1/services/adaptation/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v1/services/adaptation/__pycache__/async_client.cpython-313.pyc,,
google/cloud/speech_v1/services/adaptation/__pycache__/client.cpython-313.pyc,,
google/cloud/speech_v1/services/adaptation/__pycache__/pagers.cpython-313.pyc,,
google/cloud/speech_v1/services/adaptation/async_client.py,sha256=Z2E8KqvD9RF25oej95ak1aIZKkxU8yaUdlteKukMKpo,73111
google/cloud/speech_v1/services/adaptation/client.py,sha256=e9fniUF1ZiLXDt8Tl8KYrMV-tR10cdYE5GHp2IPUYvQ,89695
google/cloud/speech_v1/services/adaptation/pagers.py,sha256=kiz_lyimycDXzGE2mkuvjftvHJTtS7h_A0fYGP1wXcA,14505
google/cloud/speech_v1/services/adaptation/transports/__init__.py,sha256=LJGhfINJd8V2b2SFfRy5jijBwD0Onw-VmzVaR5ZhNBk,1344
google/cloud/speech_v1/services/adaptation/transports/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v1/services/adaptation/transports/__pycache__/base.cpython-313.pyc,,
google/cloud/speech_v1/services/adaptation/transports/__pycache__/grpc.cpython-313.pyc,,
google/cloud/speech_v1/services/adaptation/transports/__pycache__/grpc_asyncio.cpython-313.pyc,,
google/cloud/speech_v1/services/adaptation/transports/__pycache__/rest.cpython-313.pyc,,
google/cloud/speech_v1/services/adaptation/transports/__pycache__/rest_base.cpython-313.pyc,,
google/cloud/speech_v1/services/adaptation/transports/base.py,sha256=X7QLfKVU5W7VENQeFi9BSEB6tcDZYtQLOv5T4yamncc,11459
google/cloud/speech_v1/services/adaptation/transports/grpc.py,sha256=qx22CGhWkpShZ7NAyz2Wlg_3ICsu3ZoigyrxGYHPffc,28113
google/cloud/speech_v1/services/adaptation/transports/grpc_asyncio.py,sha256=s7a8jBfI-oRWEyy7DDNF9UgKsp5Zh8jS4DZP5N_o6fA,31572
google/cloud/speech_v1/services/adaptation/transports/rest.py,sha256=1JMR3WfE4W-KooRCUWFk1NnsJ92PESILLHX6lWMGJiA,104385
google/cloud/speech_v1/services/adaptation/transports/rest_base.py,sha256=_w0pXdqKzLefdKNdSqE49XSB-KPY9iMAG3SsfVdbaKo,23236
google/cloud/speech_v1/services/speech/__init__.py,sha256=5GEd48GerNYOl9YFwLLaFGxmQeP1EcQYnHVDmEtr31Q,737
google/cloud/speech_v1/services/speech/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v1/services/speech/__pycache__/async_client.cpython-313.pyc,,
google/cloud/speech_v1/services/speech/__pycache__/client.cpython-313.pyc,,
google/cloud/speech_v1/services/speech/async_client.py,sha256=nvXD8rlx1le4pqvywW6WC_cF7x72o32qN0m_4uUoOeI,36153
google/cloud/speech_v1/services/speech/client.py,sha256=oprZXcQtFoPOxhjsTWWL8YJTuvpDhhcywhJhXHTxos8,53326
google/cloud/speech_v1/services/speech/transports/__init__.py,sha256=ryIYQZxXYoEeBjhX3nA2ER98UArnqkioiMUzq0X8dlY,1288
google/cloud/speech_v1/services/speech/transports/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v1/services/speech/transports/__pycache__/base.cpython-313.pyc,,
google/cloud/speech_v1/services/speech/transports/__pycache__/grpc.cpython-313.pyc,,
google/cloud/speech_v1/services/speech/transports/__pycache__/grpc_asyncio.cpython-313.pyc,,
google/cloud/speech_v1/services/speech/transports/__pycache__/rest.cpython-313.pyc,,
google/cloud/speech_v1/services/speech/transports/__pycache__/rest_base.cpython-313.pyc,,
google/cloud/speech_v1/services/speech/transports/base.py,sha256=WGKNKzmdlBQRRbTXi1JhOg5sqLb3ZmDDWFaabpM-Wws,9178
google/cloud/speech_v1/services/speech/transports/grpc.py,sha256=mimfiRZuqg7DprRAUNy6QVNZmAN0IKyv-8ANNyJ3ZpA,21130
google/cloud/speech_v1/services/speech/transports/grpc_asyncio.py,sha256=9V7eiTrlWCTHXqzWNL5R4mDtzN55KplK7uE_Wpc7G6c,23877
google/cloud/speech_v1/services/speech/transports/rest.py,sha256=q3_2IqYPsghgb03mKXGtJ1bIzFotOD_1_T4xXmDSrfU,40615
google/cloud/speech_v1/services/speech/transports/rest_base.py,sha256=oo-wp5SAWOtu7poM2qSSf4kYhuPdbXG9ZsE-58xAbGs,9396
google/cloud/speech_v1/types/__init__.py,sha256=afFghqGHCklVKAx_wrWVfFs3S6XdPjgSz6Px7NVFzUQ,2653
google/cloud/speech_v1/types/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v1/types/__pycache__/cloud_speech.cpython-313.pyc,,
google/cloud/speech_v1/types/__pycache__/cloud_speech_adaptation.cpython-313.pyc,,
google/cloud/speech_v1/types/__pycache__/resource.cpython-313.pyc,,
google/cloud/speech_v1/types/cloud_speech.py,sha256=DDRwKXJC8V2LYf91pLjrbApuVveNxvB_ZJ3BndM8ICI,63294
google/cloud/speech_v1/types/cloud_speech_adaptation.py,sha256=hxLuCkdxTfje9gQRnH0ewJRlMxZFoyInWRnSe60cUGI,14117
google/cloud/speech_v1/types/resource.py,sha256=yzO2lLUgr-4FQo8lnXr51KY8CGbfmIYlIRJtSAO6_7M,10375
google/cloud/speech_v1p1beta1/__init__.py,sha256=3I48nC7NIiMt0DTMrQwsbWF1cj9U5tILsNIGJ86iYUg,3189
google/cloud/speech_v1p1beta1/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/__pycache__/gapic_version.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/gapic_metadata.json,sha256=_yze4FbBlhR-RkM7nRl6VTSoOJf1lqr4Frq2wkjCGjc,5989
google/cloud/speech_v1p1beta1/gapic_version.py,sha256=QTvlFHjTgfvGqO3KkeO-JcBtV0PKK-Cza7sVU2h7_70,653
google/cloud/speech_v1p1beta1/py.typed,sha256=9XnjN9OQ5mzBvUTS4LvkASo3060SLCUBOLJepHrwVDU,80
google/cloud/speech_v1p1beta1/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/speech_v1p1beta1/services/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/__init__.py,sha256=mc6LsNyncWccRfmKtQfcxrCI6W-4T8Kyir1Pto1c0LY,753
google/cloud/speech_v1p1beta1/services/adaptation/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/__pycache__/async_client.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/__pycache__/client.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/__pycache__/pagers.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/async_client.py,sha256=EOd0Tsoieodu3aYiPU6wCNzgCrR0W0tXZGh71xVg1Hw,73921
google/cloud/speech_v1p1beta1/services/adaptation/client.py,sha256=9EmcGJJZsGWWbqdxzEw9CmWS3WBXYoLIWAw25Yd8YDs,92008
google/cloud/speech_v1p1beta1/services/adaptation/pagers.py,sha256=bfRhVZ0FaWIJGppKnVd-96nI10CbNcAz7N1qim8C9Tg,14624
google/cloud/speech_v1p1beta1/services/adaptation/transports/__init__.py,sha256=LJGhfINJd8V2b2SFfRy5jijBwD0Onw-VmzVaR5ZhNBk,1344
google/cloud/speech_v1p1beta1/services/adaptation/transports/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/transports/__pycache__/base.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/transports/__pycache__/grpc.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/transports/__pycache__/grpc_asyncio.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/transports/__pycache__/rest.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/transports/__pycache__/rest_base.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/adaptation/transports/base.py,sha256=wZZnnxg0WTPTInGyIrHs5rebtg6-fEH6Fj_fJANM6-c,11473
google/cloud/speech_v1p1beta1/services/adaptation/transports/grpc.py,sha256=LIhHKm70yz_QXPZAfMkTGjmpaGXlmk_sVxZFt0r8zUc,28204
google/cloud/speech_v1p1beta1/services/adaptation/transports/grpc_asyncio.py,sha256=q9UXfHeqX9AiEK8WyxeZpS4Otaw1WJvIDvdrRahDrf4,31663
google/cloud/speech_v1p1beta1/services/adaptation/transports/rest.py,sha256=5K0Uuc0OOpoz9m6sKtaYZtGLX4PA8XlfhWXhD5qrWis,104700
google/cloud/speech_v1p1beta1/services/adaptation/transports/rest_base.py,sha256=szemMwAHOR2WL4cC0TaPLEjUbLlDz-QKqh6OaSx6PJM,23327
google/cloud/speech_v1p1beta1/services/speech/__init__.py,sha256=5GEd48GerNYOl9YFwLLaFGxmQeP1EcQYnHVDmEtr31Q,737
google/cloud/speech_v1p1beta1/services/speech/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/speech/__pycache__/async_client.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/speech/__pycache__/client.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/speech/async_client.py,sha256=pEEAriZWD8AhxhpbRkZewoJ-og-8zXufs0Ief9GX9SM,36693
google/cloud/speech_v1p1beta1/services/speech/client.py,sha256=3SV6RlODiFpM6snnk20-KWpLlSpX3ALExuyahxiwxNw,55385
google/cloud/speech_v1p1beta1/services/speech/transports/__init__.py,sha256=ryIYQZxXYoEeBjhX3nA2ER98UArnqkioiMUzq0X8dlY,1288
google/cloud/speech_v1p1beta1/services/speech/transports/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/speech/transports/__pycache__/base.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/speech/transports/__pycache__/grpc.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/speech/transports/__pycache__/grpc_asyncio.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/speech/transports/__pycache__/rest.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/speech/transports/__pycache__/rest_base.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/services/speech/transports/base.py,sha256=HbDT8FTR67kG_Eskv6nwzTmnnf7l-vcGNgp7ZvvPCws,9192
google/cloud/speech_v1p1beta1/services/speech/transports/grpc.py,sha256=LXqxDii8-s2R5Gu83HOoXvtM2rYTjeWESm_QJ-BL5M4,21172
google/cloud/speech_v1p1beta1/services/speech/transports/grpc_asyncio.py,sha256=0BBq5XxLFAui6vBV4UL_qD44o0qXajQU5Gng6jTsaig,23919
google/cloud/speech_v1p1beta1/services/speech/transports/rest.py,sha256=Ku1LvedNiSNYGnON6BZEVlfi17c3Un152Yp-1O0rza0,40755
google/cloud/speech_v1p1beta1/services/speech/transports/rest_base.py,sha256=z2FG-bqLJRBTFBhKH-cjuzNCdMR4oAKDEjSRE_kTU24,9431
google/cloud/speech_v1p1beta1/types/__init__.py,sha256=afFghqGHCklVKAx_wrWVfFs3S6XdPjgSz6Px7NVFzUQ,2653
google/cloud/speech_v1p1beta1/types/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/types/__pycache__/cloud_speech.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/types/__pycache__/cloud_speech_adaptation.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/types/__pycache__/resource.cpython-313.pyc,,
google/cloud/speech_v1p1beta1/types/cloud_speech.py,sha256=6BBj2ZP9KbjSo-B0FBqhpmyBMTSbdcD9Fa1SZqDFc6E,65365
google/cloud/speech_v1p1beta1/types/cloud_speech_adaptation.py,sha256=xQsp-PeTR0d1GQJzyZQQMSwslyLjtqRBXkb_93PLY-w,14173
google/cloud/speech_v1p1beta1/types/resource.py,sha256=T6ozCpLV0s6JxFYncYNS_BFYhI4NYTGR9r50-ZqJQH0,18251
google/cloud/speech_v2/__init__.py,sha256=fX_gnkeTCIiDVJdbUn_6sDnTSFGOOxJ7zZXrh7Ye5K8,4712
google/cloud/speech_v2/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v2/__pycache__/gapic_version.cpython-313.pyc,,
google/cloud/speech_v2/gapic_metadata.json,sha256=bguz80S758ODnrA5g8PZCoXUjgbYgTzpO39C-sHs4wY,9349
google/cloud/speech_v2/gapic_version.py,sha256=QTvlFHjTgfvGqO3KkeO-JcBtV0PKK-Cza7sVU2h7_70,653
google/cloud/speech_v2/py.typed,sha256=9XnjN9OQ5mzBvUTS4LvkASo3060SLCUBOLJepHrwVDU,80
google/cloud/speech_v2/services/__init__.py,sha256=ABnBlq63X5TtiLc0JptfQUECNGc-s0WTBlbk0_83qf0,600
google/cloud/speech_v2/services/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v2/services/speech/__init__.py,sha256=5GEd48GerNYOl9YFwLLaFGxmQeP1EcQYnHVDmEtr31Q,737
google/cloud/speech_v2/services/speech/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v2/services/speech/__pycache__/async_client.cpython-313.pyc,,
google/cloud/speech_v2/services/speech/__pycache__/client.cpython-313.pyc,,
google/cloud/speech_v2/services/speech/__pycache__/pagers.cpython-313.pyc,,
google/cloud/speech_v2/services/speech/async_client.py,sha256=DmZZPTBQr5NzCJFqqxi1RuPxfH72eNArjghJ1XZVpdE,163770
google/cloud/speech_v2/services/speech/client.py,sha256=fIYeAw91odcm6F1GVg4KMOHfWAry9SIMOlTKuQwEPZ4,181439
google/cloud/speech_v2/services/speech/pagers.py,sha256=xFh7p4p89FSNEFi7jbvLln-zoYcd-iSK9cIf08oKA0M,20688
google/cloud/speech_v2/services/speech/transports/__init__.py,sha256=ryIYQZxXYoEeBjhX3nA2ER98UArnqkioiMUzq0X8dlY,1288
google/cloud/speech_v2/services/speech/transports/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v2/services/speech/transports/__pycache__/base.cpython-313.pyc,,
google/cloud/speech_v2/services/speech/transports/__pycache__/grpc.cpython-313.pyc,,
google/cloud/speech_v2/services/speech/transports/__pycache__/grpc_asyncio.cpython-313.pyc,,
google/cloud/speech_v2/services/speech/transports/__pycache__/rest.cpython-313.pyc,,
google/cloud/speech_v2/services/speech/transports/__pycache__/rest_base.cpython-313.pyc,,
google/cloud/speech_v2/services/speech/transports/base.py,sha256=SL4UraMsAD8mor2VMCEKr9wgdj8_oS7WqT4gON5LMcU,19009
google/cloud/speech_v2/services/speech/transports/grpc.py,sha256=xNUVNfLnOVuzx8jfTP29zMsgayG53acYKxjlDaxYCqM,47108
google/cloud/speech_v2/services/speech/transports/grpc_asyncio.py,sha256=o0XgmNLClu-zfUnTRi9K_X2seMM9Dm6T7s5myjhR8KM,54206
google/cloud/speech_v2/services/speech/transports/rest.py,sha256=4zhImpcL_gY7lwQGd5Iymt7dMOTqgTSUfkGLom7UMzE,245774
google/cloud/speech_v2/services/speech/transports/rest_base.py,sha256=rAIvl33qhdPWpvYVTBG3Gz_Tu3-7wyWdLH-BvQ5mLqA,49256
google/cloud/speech_v2/types/__init__.py,sha256=y1Dae1pQSyS3veqJfOKkFa_05PqWQhaIIvomTTmkOT0,4481
google/cloud/speech_v2/types/__pycache__/__init__.cpython-313.pyc,,
google/cloud/speech_v2/types/__pycache__/cloud_speech.cpython-313.pyc,,
google/cloud/speech_v2/types/__pycache__/locations_metadata.cpython-313.pyc,,
google/cloud/speech_v2/types/cloud_speech.py,sha256=h_xsv_CB2zqtsEmXvk6InY9bL0Hv_oBCTsM65Yg1JQ4,122568
google/cloud/speech_v2/types/locations_metadata.py,sha256=CYbbkTF7W_NWKdigwRCo_q3rc4byd8hwyfmTNCeJnwk,4980
google_cloud_speech-2.32.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
google_cloud_speech-2.32.0.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
google_cloud_speech-2.32.0.dist-info/METADATA,sha256=InB7je1wz0P-3v512y7Cw2wZiRzmwROFDEdbPlFNmbY,9452
google_cloud_speech-2.32.0.dist-info/RECORD,,
google_cloud_speech-2.32.0.dist-info/WHEEL,sha256=P9jw-gEje8ByB7_hXoICnHtVCrEwMQh-630tKvQWehc,91
google_cloud_speech-2.32.0.dist-info/top_level.txt,sha256=_1QvSJIhFAGfxb79D6DhB7SUw2X6T4rwnz_LLrbcD3c,7
