google/cloud/bigquery/__init__.py,sha256=3J0Ud73Hl01xvemMCqKOD5dKgMEPIeJ05YcZ91a1AyM,9360
google/cloud/bigquery/__pycache__/__init__.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/_helpers.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/_http.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/_job_helpers.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/_pandas_helpers.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/_pyarrow_helpers.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/_tqdm_helpers.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/_versions_helpers.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/client.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/dataset.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/encryption_configuration.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/enums.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/exceptions.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/external_config.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/format_options.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/iam.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/model.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/opentelemetry_tracing.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/query.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/retry.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/schema.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/standard_sql.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/table.cpython-313.pyc,,
google/cloud/bigquery/__pycache__/version.cpython-313.pyc,,
google/cloud/bigquery/_helpers.py,sha256=vCFmyEN8ksY6Ss8OHn-RWKpImzYvsX3f6IUlU3Lmlxw,38092
google/cloud/bigquery/_http.py,sha256=QL1Yny3KKQM1_PbUvC1hT4gr1cEVVk0tN45Hlw0pMlY,2039
google/cloud/bigquery/_job_helpers.py,sha256=VQkVzrXBa810FutG42atztY3MCZ34LqKgq9EHI6-rHY,24610
google/cloud/bigquery/_pandas_helpers.py,sha256=MJML0k8azm6jJWLnoMHi09GCRUSjrdrk__6QEtzyFks,42116
google/cloud/bigquery/_pyarrow_helpers.py,sha256=GMAgsGvyF47KieII74WIbVA-PNdkeHlFxQ8RYIOBL-U,5240
google/cloud/bigquery/_tqdm_helpers.py,sha256=jNEwq7VFfvKXgUTcCOSpR4DRyeoadXJEgSEMd7kf7eU,4668
google/cloud/bigquery/_versions_helpers.py,sha256=dX7HZnVXFm5LWzc8oNVDWAfAn0YaN17iUXzUwtWmU3A,9645
google/cloud/bigquery/client.py,sha256=_I47S7tBt5wLgKA3QHKy5XF02beoBdbpRlS7DCqprZI,173415
google/cloud/bigquery/dataset.py,sha256=2WPyWN5HC35oOVMqlQVeGTFfXfri-Y6Bsg3Mz9G5VWE,45392
google/cloud/bigquery/dbapi/__init__.py,sha256=Q3j6V96x0ozjxDQdGfMYvg_cFAOK39yBq1OAnwse8Wo,2980
google/cloud/bigquery/dbapi/__pycache__/__init__.cpython-313.pyc,,
google/cloud/bigquery/dbapi/__pycache__/_helpers.cpython-313.pyc,,
google/cloud/bigquery/dbapi/__pycache__/connection.cpython-313.pyc,,
google/cloud/bigquery/dbapi/__pycache__/cursor.cpython-313.pyc,,
google/cloud/bigquery/dbapi/__pycache__/exceptions.cpython-313.pyc,,
google/cloud/bigquery/dbapi/__pycache__/types.cpython-313.pyc,,
google/cloud/bigquery/dbapi/_helpers.py,sha256=alFB6wD2BGMLmXIxoWtAV2jwz_yXz2HVBc_jls4pmXY,17052
google/cloud/bigquery/dbapi/connection.py,sha256=q0A_GP5nfOzZS2oGjqiqWpXxhVknMKkT_b-JjFtBP-g,4960
google/cloud/bigquery/dbapi/cursor.py,sha256=-gKLx9fCSI_I931TM5gfUlqdKxOjNb_UdkZdK7lqC04,20509
google/cloud/bigquery/dbapi/exceptions.py,sha256=xPTDFI2LCyRLKcKm4m11rCvrBd901tKrOjHQWi_MRV0,1685
google/cloud/bigquery/dbapi/types.py,sha256=_7fg9256shsJTOP6iaGT0-HYx2xXCZ46xZNbF6-oNXw,2769
google/cloud/bigquery/encryption_configuration.py,sha256=BB5hNwwxFy41saz1dyJ4QDBrQO3QBi2JRC3zGOnzJTc,2635
google/cloud/bigquery/enums.py,sha256=LvTjbVsjhv_pI4gMlDNZAYmVLWUrfg1qOjZPEg4BAO0,12142
google/cloud/bigquery/exceptions.py,sha256=Ws-0LrrhIezjEXKp7I6J-vQFm44P0OkFNFOG_DZuHkA,1228
google/cloud/bigquery/external_config.py,sha256=MLJR60p0vSiT7VWJ8dKia0lJiv55YlJkFFxppvPblPc,41142
google/cloud/bigquery/format_options.py,sha256=TOkz_lXMlJWJXq5-OQKNfO_Ak-5tI94lduCDG8ULIjE,4901
google/cloud/bigquery/iam.py,sha256=w2d82vjPO78pQriflvU9Kd5CQVNNdvXu1Ykjj6ftOSc,1554
google/cloud/bigquery/job/__init__.py,sha256=F6tw1yEmdk6qOqLYT9hY4Vno4X3iaLXAJ97-3cWKXx0,3270
google/cloud/bigquery/job/__pycache__/__init__.cpython-313.pyc,,
google/cloud/bigquery/job/__pycache__/base.cpython-313.pyc,,
google/cloud/bigquery/job/__pycache__/copy_.cpython-313.pyc,,
google/cloud/bigquery/job/__pycache__/extract.cpython-313.pyc,,
google/cloud/bigquery/job/__pycache__/load.cpython-313.pyc,,
google/cloud/bigquery/job/__pycache__/query.cpython-313.pyc,,
google/cloud/bigquery/job/base.py,sha256=RcSQqxdlnqTujh7UgLglSell3kyveyQxGymPBK21y-o,39014
google/cloud/bigquery/job/copy_.py,sha256=yCpkUDha5AxRztC4C8raejs8uq2x5YzDXTgcw52dyN0,10125
google/cloud/bigquery/job/extract.py,sha256=BF11RegU_b6TyEc-sz4CkAjnfvF4smvgyy80AQSBWcg,9573
google/cloud/bigquery/job/load.py,sha256=PlEjqEV83iJa9qT5VswVEaYDhQZU5jFldDX3IYuMq4E,34517
google/cloud/bigquery/job/query.py,sha256=_yDc3akMfCkgORXY3S34Cf6dIFY_MIIMPRQEcOSRthE,99391
google/cloud/bigquery/magics/__init__.py,sha256=ML6JJ9xOS0xWS-c6vT3BdC2RFXrzpuj9RDsgV7SDffw,775
google/cloud/bigquery/magics/__pycache__/__init__.cpython-313.pyc,,
google/cloud/bigquery/magics/__pycache__/magics.cpython-313.pyc,,
google/cloud/bigquery/magics/line_arg_parser/__init__.py,sha256=jMf9Dwa1uPkN5e5W36zcKQASYg6p9Da37-ZvhNKEWUI,1252
google/cloud/bigquery/magics/line_arg_parser/__pycache__/__init__.cpython-313.pyc,,
google/cloud/bigquery/magics/line_arg_parser/__pycache__/exceptions.cpython-313.pyc,,
google/cloud/bigquery/magics/line_arg_parser/__pycache__/lexer.cpython-313.pyc,,
google/cloud/bigquery/magics/line_arg_parser/__pycache__/parser.cpython-313.pyc,,
google/cloud/bigquery/magics/line_arg_parser/__pycache__/visitors.cpython-313.pyc,,
google/cloud/bigquery/magics/line_arg_parser/exceptions.py,sha256=TFEA8d8tJKJHvwo1mLiYF-CGDpFGow0fdekauw_KTQo,779
google/cloud/bigquery/magics/line_arg_parser/lexer.py,sha256=3OxoPuMFQJAqMlLyssOIW9KDgsLY1VLsCxTbP5lSWek,7948
google/cloud/bigquery/magics/line_arg_parser/parser.py,sha256=rwjCiivLeGEP2vfplWvxRsdFtLvxONLJvA8Z64JzOL0,15999
google/cloud/bigquery/magics/line_arg_parser/visitors.py,sha256=XXHk9R-3f97qwASVBEkVuxgu8NQSisEScs6Coi5gExo,5151
google/cloud/bigquery/magics/magics.py,sha256=Jm40QJftitCFJR5IgV5y8_Fxzeav96cGlV_1-i4TiE4,26660
google/cloud/bigquery/model.py,sha256=tOP3f6nB7THa3J34WD9xFFzI4XPJtDHRX0i9Q0Fb6VA,17102
google/cloud/bigquery/opentelemetry_tracing.py,sha256=kEy-m8xD0Kk1RDhKT-a3-NMoj3er5G5QiacyibmXmsY,6101
google/cloud/bigquery/py.typed,sha256=6fUaZ4QqCRbkYNrMxSPvYN7P3PdkkNtPjyLdimHatBc,82
google/cloud/bigquery/query.py,sha256=dMyLVprY4t4QZPWK0R-1AsSrDJvWP6EG8fa4KocRuYY,46597
google/cloud/bigquery/retry.py,sha256=kG-JM0pl_LJES86nU3EwBN3q1mfe9RJYYEff9ilGSpA,7789
google/cloud/bigquery/routine/__init__.py,sha256=1_bQ2j78eB17B_MXJMbjr_ncjEG7crX3UVcd-wrE4Gk,1138
google/cloud/bigquery/routine/__pycache__/__init__.cpython-313.pyc,,
google/cloud/bigquery/routine/__pycache__/routine.cpython-313.pyc,,
google/cloud/bigquery/routine/routine.py,sha256=LYpzwGbtv0iOtxfYT8vxdUTP_JwsYIxYpnu3JfzOwUs,25195
google/cloud/bigquery/schema.py,sha256=8GRkpua2UsPB1NOqc_NC_jl7oF_0kMAl3KXSxyGJ4Cg,32422
google/cloud/bigquery/standard_sql.py,sha256=iVOQs2WBKecLmtA4acma-uKOf-L77_qBzwaGJ8rwAho,13574
google/cloud/bigquery/table.py,sha256=gOM4x4gFqTKz4I7WXvPwD5RCg9jYuEDAln_kimSf2U4,143228
google/cloud/bigquery/version.py,sha256=Hj_eTb_FZ8Wzxv1n4xipXUTzC7LarV9FIyESAsMkDOQ,598
google/cloud/bigquery_v2/__init__.py,sha256=3fu-g0AXJpcodVM-NlklHSchgc7MvLNvL3cmIqwoUJ0,1782
google/cloud/bigquery_v2/__pycache__/__init__.cpython-313.pyc,,
google/cloud/bigquery_v2/types/__init__.py,sha256=CzvM5-4tpyAvrTJznzVlVoSeF91-LnRG7srS3_Fc2iY,1385
google/cloud/bigquery_v2/types/__pycache__/__init__.cpython-313.pyc,,
google/cloud/bigquery_v2/types/__pycache__/encryption_config.cpython-313.pyc,,
google/cloud/bigquery_v2/types/__pycache__/model.cpython-313.pyc,,
google/cloud/bigquery_v2/types/__pycache__/model_reference.cpython-313.pyc,,
google/cloud/bigquery_v2/types/__pycache__/standard_sql.cpython-313.pyc,,
google/cloud/bigquery_v2/types/__pycache__/table_reference.cpython-313.pyc,,
google/cloud/bigquery_v2/types/encryption_config.py,sha256=ToZ_7KNCeN9NEJ6qC46i3ITlGguw6KXvWaMTbpjwLTE,1399
google/cloud/bigquery_v2/types/model.py,sha256=B5juTZHBpywvyDZn0b4SfBtObvxvo240pEur4ooHUwo,75103
google/cloud/bigquery_v2/types/model_reference.py,sha256=LdV5ofA17ov0IVpw5_vqFWkjww_T2p8piccWW_6CWUE,1539
google/cloud/bigquery_v2/types/standard_sql.py,sha256=h9-2p1UMhnjJzpIk_6SYDJnsN5FbfawyzQKJvHa5KvQ,4428
google/cloud/bigquery_v2/types/table_reference.py,sha256=0jOHzgto1w86PYfru7oHqk_MBEpEqxKTYqQ6tfBpm1c,2509
google_cloud_bigquery-3.33.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
google_cloud_bigquery-3.33.0.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
google_cloud_bigquery-3.33.0.dist-info/METADATA,sha256=h8elAy3D82XN9lvxj7DW7whvcopDKuVSSeB1-yQBthI,7956
google_cloud_bigquery-3.33.0.dist-info/RECORD,,
google_cloud_bigquery-3.33.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
google_cloud_bigquery-3.33.0.dist-info/WHEEL,sha256=P9jw-gEje8ByB7_hXoICnHtVCrEwMQh-630tKvQWehc,91
google_cloud_bigquery-3.33.0.dist-info/top_level.txt,sha256=_1QvSJIhFAGfxb79D6DhB7SUw2X6T4rwnz_LLrbcD3c,7
