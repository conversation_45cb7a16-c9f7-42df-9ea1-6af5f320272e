Metadata-Version: 2.1
Name: opentelemetry-resourcedetector-gcp
Version: 1.9.0a0
Summary: Google Cloud resource detector for OpenTelemetry
Home-page: https://github.com/GoogleCloudPlatform/opentelemetry-operations-python/tree/main/opentelemetry-resourcedetector-gcp
Author: Google
Author-email: <EMAIL>
License: Apache-2.0
Platform: any
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.7
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: opentelemetry-api ~=1.0
Requires-Dist: opentelemetry-sdk ~=1.0
Requires-Dist: requests ~=2.24
Requires-Dist: typing-extensions ~=4.0
Provides-Extra: test

OpenTelemetry Google Cloud Resource Detector
============================================

.. image:: https://badge.fury.io/py/opentelemetry-resourcedetector-gcp.svg
    :target: https://badge.fury.io/py/opentelemetry-resourcedetector-gcp

.. image:: https://readthedocs.org/projects/google-cloud-opentelemetry/badge/?version=latest
    :target: https://google-cloud-opentelemetry.readthedocs.io/en/latest/?badge=latest
    :alt: Documentation Status

This library provides support for detecting GCP resources like GCE, GKE, etc.

To get started with instrumentation in Google Cloud, see `Generate traces and metrics with
Python <https://cloud.google.com/stackdriver/docs/instrumentation/setup/python>`_.

To learn more about instrumentation and observability, including opinionated recommendations
for Google Cloud Observability, visit `Instrumentation and observability
<https://cloud.google.com/stackdriver/docs/instrumentation/overview>`_.

Installation
------------

.. code:: bash

    pip install opentelemetry-resourcedetector-gcp

..
    TODO: Add usage info here


References
----------

* `Cloud Monitoring <https://cloud.google.com/monitoring>`_
* `OpenTelemetry Project <https://opentelemetry.io/>`_
