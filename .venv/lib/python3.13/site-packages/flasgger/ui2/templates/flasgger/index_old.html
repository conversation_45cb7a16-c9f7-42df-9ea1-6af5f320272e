<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Flasgger</title>
  <link rel="icon" type="image/png" href="{{url_for('flasgger.static', filename='')}}images/favicon-32x32.png" sizes="32x32" />
  <link rel="icon" type="image/png" href="images/favicon-16x16.png" sizes="16x16" />
  <link href='css/typography.css' media='screen' rel='stylesheet' type='text/css'/>
  <link href='css/reset.css' media='screen' rel='stylesheet' type='text/css'/>
  <link href='css/screen.css' media='screen' rel='stylesheet' type='text/css'/>
  <link href='css/reset.css' media='print' rel='stylesheet' type='text/css'/>
  <link href='css/print.css' media='print' rel='stylesheet' type='text/css'/>
  <script src='js/jquery-1.8.0.min.js' type='text/javascript'></script>
  <script src='js/jquery.slideto.min.js' type='text/javascript'></script>
  <script src='js/jquery.wiggle.min.js' type='text/javascript'></script>
  <script src='js/jquery.ba-bbq.min.js' type='text/javascript'></script>
  <script src='js/handlebars-2.0.0.js' type='text/javascript'></script>
  <script src='js/underscore-min.js' type='text/javascript'></script>
  <script src='js/backbone-min.js' type='text/javascript'></script>
  <script src='swagger-ui.min.js' type='text/javascript'></script>
  <script src='js/highlight.7.3.pack.js' type='text/javascript'></script>
  <script src='js/marked.js' type='text/javascript'></script>
  <script src='js/swagger-oauth.js' type='text/javascript'></script>

  <!-- Some basic translations -->
  <!-- <script src='lang/translator.js' type='text/javascript'></script> -->
  <!-- <script src='lang/ru.js' type='text/javascript'></script> -->
  <!-- <script src='lang/en.js' type='text/javascript'></script> -->

  <script type="text/javascript">
    var specs_url = "/specs";
    var first_url = "/spec";
    function load_specs(){
        $.getJSON( specs_url, function( data ) {
          // GAMBI
          var items = ["<option value='" + data['specs'][0].url +"'> versions </option>"];

          $.each( data['specs'], function( key, val ) {
            items.push( "<option value='" + val.url + "'>" + val.title + "</option>" );
          });

          $( "<select/>", {
            "class": "spec_selector",
            "id": "spec_selector",
            html: items.join( "" )
          }).appendTo( "#spec_selector_div" );

          $('#logo').html(data['title']);
          document.title = data['title'] + ' | API documentation';
        });
    }

    function redirect_to(url){
      window.location.href = '/apidocs/index.html?url=' + url;
    }

    function redirect_to_selected(element){
      console.log(element)
    }

    $(function () {

      load_specs()

      var url = window.location.search.match(/url=([^&]+)/);
      if (url && url.length > 1) {
        url = decodeURIComponent(url[1]);
      } else {
        url = '/spec';
      }

      // Pre load translate...
      if(window.SwaggerTranslator) {
        window.SwaggerTranslator.translate();
      }
      window.swaggerUi = new SwaggerUi({
        url: url,
        dom_id: "swagger-ui-container",
        supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
        onComplete: function(swaggerApi, swaggerUi){
          if(typeof initOAuth == "function") {
            initOAuth({
              clientId: "your-client-id",
              clientSecret: "your-client-secret",
              realm: "your-realms",
              appName: "your-app-name",
              scopeSeparator: ","
            });
          }

          if(window.SwaggerTranslator) {
            window.SwaggerTranslator.translate();
          }

          $('pre code').each(function(i, e) {
            hljs.highlightBlock(e)
          });

          addApiKeyAuthorization();

          $('.spec_selector').on('change', function (e) {
              var optionSelected = $("option:selected", this);
              var valueSelected = this.value;
              redirect_to(valueSelected);
          });
        },
        onFailure: function(data) {
          log("Unable to Load SwaggerUI");
          // GAMBI
          redirect_to($('#spec_selector').val());
        },
        docExpansion: "none",
        apisSorter: "alpha",
        showRequestHeaders: false,
        validatorUrl: null
      });

      function addApiKeyAuthorization(){
        var key = encodeURIComponent($('#input_apiKey')[0].value);
        if(key && key.trim() != "") {
            var apiKeyAuth = new SwaggerClient.ApiKeyAuthorization("api_key", key, "query");
            window.swaggerUi.api.clientAuthorizations.add("api_key", apiKeyAuth);
            log("added key " + key);
        }
      }

      $('#input_apiKey').change(addApiKeyAuthorization);

      // if you have an apiKey you would like to pre-populate on the page for demonstration purposes...
      /*
        var apiKey = "myApiKeyXXXX123456789";
        $('#input_apiKey').val(apiKey);
      */

      window.swaggerUi.load();

      function log() {
        if ('console' in window) {
          console.log.apply(console, arguments);
        }
      }

      // $('.spec_selector').on('change', function (e) {
      //     var optionSelected = $("option:selected", this);
      //     var valueSelected = this.value;
      //     console.log(valueSelected)
      // });


  });
  </script>
</head>

<body class="swagger-section">
<div id='header'>
  <div class="swagger-ui-wrap">
    <a id="logo" href="index.html">flasgger</a>
    <form id='api_selector'>
      <div class='input' id="spec_selector_div"></div>
      <div class='input'><input placeholder="http://example.com/api" id="input_baseUrl" name="baseUrl" type="text"/></div>
      <div class='input'><input placeholder="api_key" id="input_apiKey" name="apiKey" type="text"/></div>
      <div class='input'><a id="explore" href="#" data-sw-translate>Explore</a></div>
    </form>
  </div>
</div>

<div id="message-bar" class="swagger-ui-wrap" data-sw-translate>&nbsp;</div>
<div id="swagger-ui-container" class="swagger-ui-wrap"></div>
<div class="swagger-ui-wrap footer">
<small style="font-size: 60%; color: #ccc;">
[Powered by <a href="https://github.com/rochacbruno/flasgger">Flasgger</a>]
</small>
</div>
</body>
</html>
