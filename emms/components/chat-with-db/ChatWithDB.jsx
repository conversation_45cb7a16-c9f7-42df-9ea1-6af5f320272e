import React, { useState, useRef, useEffect } from 'react';
import { <PERSON>, But<PERSON>, Container, Flex, Heading, Input, Text, VStack, H<PERSON><PERSON>ck, Spinner, Code, Divider } from '@chakra-ui/react';
import { ChevronRightIcon } from '@chakra-ui/icons';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

const ChatWithDB = () => {
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const messagesEndRef = useRef(null);
  const eventSourceRef = useRef(null);

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Clean up event source on unmount
  useEffect(() => {
    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!input.trim()) return;

    const userMessage = { role: 'user', content: input };
    setMessages((prev) => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);
    setError(null);

    // Create initial AI message
    const aiMessageId = Date.now().toString();
    const aiMessage = {
      id: aiMessageId,
      role: 'assistant',
      content: '',
      sql: '',
      results: null,
      status: 'thinking',
    };
    setMessages((prev) => [...prev, aiMessage]);

    try {
      // Close any existing event source
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }

      // Create new event source for streaming
      const eventSource = new EventSource(
        `${API_URL}/api/chat/ask/stream?question=${encodeURIComponent(input)}&db_type=postgres`
      );
      eventSourceRef.current = eventSource;

      eventSource.onmessage = (event) => {
        const data = JSON.parse(event.data);
        
        setMessages((prevMessages) => {
          const updatedMessages = [...prevMessages];
          const aiMessageIndex = updatedMessages.findIndex((msg) => msg.id === aiMessageId);
          
          if (aiMessageIndex !== -1) {
            const updatedAiMessage = { ...updatedMessages[aiMessageIndex] };
            
            // Update message based on the data received
            if (data.status) {
              updatedAiMessage.status = data.status;
            }
            
            if (data.sql) {
              updatedAiMessage.sql = data.sql;
            }
            
            if (data.results) {
              updatedAiMessage.results = data.results;
            }
            
            if (data.explanation_chunk) {
              updatedAiMessage.content += data.explanation_chunk;
            }
            
            if (data.complete) {
              updatedAiMessage.status = 'complete';
              updatedAiMessage.content = data.explanation;
              updatedAiMessage.sql = data.sql;
              updatedAiMessage.results = data.results;
              eventSource.close();
            }
            
            updatedMessages[aiMessageIndex] = updatedAiMessage;
          }
          
          return updatedMessages;
        });
      };

      eventSource.onerror = (error) => {
        console.error('EventSource error:', error);
        setError('Error connecting to the server. Please try again.');
        eventSource.close();
        setIsLoading(false);
      };

      // When the stream completes
      eventSource.addEventListener('done', () => {
        eventSource.close();
        setIsLoading(false);
      });
    } catch (err) {
      console.error('Error:', err);
      setError(err.message || 'An error occurred. Please try again.');
      setIsLoading(false);
    }
  };

  // Render SQL results as a table
  const renderResults = (results) => {
    if (!results || !results.results || !Array.isArray(results.results) || results.results.length === 0) {
      return <Text>No results</Text>;
    }

    const columns = results.columns || Object.keys(results.results[0]);

    return (
      <Box overflowX="auto" my={4}>
        <table style={{ borderCollapse: 'collapse', width: '100%' }}>
          <thead>
            <tr>
              {columns.map((col, i) => (
                <th key={i} style={{ border: '1px solid #ddd', padding: '8px', textAlign: 'left' }}>
                  {col}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {results.results.slice(0, 10).map((row, i) => (
              <tr key={i}>
                {columns.map((col, j) => (
                  <td key={j} style={{ border: '1px solid #ddd', padding: '8px' }}>
                    {row[col] !== null ? String(row[col]) : 'NULL'}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
        {results.results.length > 10 && (
          <Text mt={2} fontSize="sm" color="gray.500">
            Showing 10 of {results.results.length} results
          </Text>
        )}
      </Box>
    );
  };

  return (
    <Container maxW="container.lg" py={8}>
      <Heading as="h1" mb={6} textAlign="center">
        Chat with Database
      </Heading>
      
      <Box
        borderWidth={1}
        borderRadius="lg"
        p={4}
        height="600px"
        display="flex"
        flexDirection="column"
      >
        <VStack
          flex={1}
          spacing={4}
          align="stretch"
          overflowY="auto"
          px={2}
          py={4}
        >
          {messages.length === 0 ? (
            <Text color="gray.500" textAlign="center">
              Ask a question about your database
            </Text>
          ) : (
            messages.map((message, index) => (
              <Box
                key={index}
                alignSelf={message.role === 'user' ? 'flex-end' : 'flex-start'}
                bg={message.role === 'user' ? 'blue.500' : 'gray.100'}
                color={message.role === 'user' ? 'white' : 'black'}
                borderRadius="lg"
                p={3}
                maxW="80%"
              >
                {message.role === 'user' ? (
                  <Text>{message.content}</Text>
                ) : (
                  <VStack align="start" spacing={3}>
                    {message.status === 'thinking' || message.status === 'generating_sql' ? (
                      <HStack>
                        <Spinner size="sm" />
                        <Text>Generating SQL...</Text>
                      </HStack>
                    ) : message.status === 'executing_sql' ? (
                      <HStack>
                        <Spinner size="sm" />
                        <Text>Executing SQL...</Text>
                      </HStack>
                    ) : message.status === 'generating_explanation' ? (
                      <HStack>
                        <Spinner size="sm" />
                        <Text>Generating explanation...</Text>
                      </HStack>
                    ) : null}

                    {message.sql && (
                      <Box w="100%">
                        <Text fontWeight="bold" mb={1}>
                          SQL Query:
                        </Text>
                        <Code p={2} borderRadius="md" w="100%" whiteSpace="pre-wrap">
                          {message.sql}
                        </Code>
                      </Box>
                    )}

                    {message.results && (
                      <Box w="100%">
                        <Text fontWeight="bold" mb={1}>
                          Results:
                        </Text>
                        {renderResults(message.results)}
                      </Box>
                    )}

                    {message.content && (
                      <Box w="100%">
                        <Divider my={2} />
                        <Text>{message.content}</Text>
                      </Box>
                    )}
                  </VStack>
                )}
              </Box>
            ))
          )}
          <div ref={messagesEndRef} />
        </VStack>

        <form onSubmit={handleSubmit}>
          <HStack mt={4}>
            <Input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder="Ask a question about your database..."
              disabled={isLoading}
            />
            <Button
              type="submit"
              colorScheme="blue"
              isLoading={isLoading}
              disabled={isLoading || !input.trim()}
            >
              <ChevronRightIcon />
            </Button>
          </HStack>
        </form>

        {error && (
          <Text color="red.500" mt={2}>
            {error}
          </Text>
        )}
      </Box>
    </Container>
  );
};

export default ChatWithDB;
