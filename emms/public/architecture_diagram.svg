<svg id="mermaid-1605862734911" width="100%" xmlns="http://www.w3.org/2000/svg" height="401" style="max-width: 1046px; background-color:#FFF" viewBox="-50 -10 1046 401"><style>#mermaid-1605862734911{font-family:"trebuchet ms",verdana,arial;font-size:16px;fill:#000000;}#mermaid-1605862734911 .error-icon{fill:#552222;}#mermaid-1605862734911 .error-text{fill:#552222;stroke:#552222;}#mermaid-1605862734911 .edge-thickness-normal{stroke-width:2px;}#mermaid-1605862734911 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-1605862734911 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-1605862734911 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-1605862734911 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-1605862734911 .marker{fill:#666;}#mermaid-1605862734911 .marker.cross{stroke:#666;}#mermaid-1605862734911 svg{font-family:"trebuchet ms",verdana,arial;font-size:16px;}#mermaid-1605862734911 .actor{stroke:hsl(0,0%,83%);fill:#eee;}#mermaid-1605862734911 text.actor &gt; tspan{fill:#333;stroke:none;}#mermaid-1605862734911 .actor-line{stroke:#666;}#mermaid-1605862734911 .messageLine0{stroke-width:1.5;stroke-dasharray:none;stroke:#333;}#mermaid-1605862734911 .messageLine1{stroke-width:1.5;stroke-dasharray:2,2;stroke:#333;}#mermaid-1605862734911 #arrowhead path{fill:#333;stroke:#333;}#mermaid-1605862734911 .sequenceNumber{fill:white;}#mermaid-1605862734911 #sequencenumber{fill:#333;}#mermaid-1605862734911 #crosshead path{fill:#333;stroke:#333;}#mermaid-1605862734911 .messageText{fill:#333;stroke:#333;}#mermaid-1605862734911 .labelBox{stroke:hsl(0,0%,83%);fill:#eee;}#mermaid-1605862734911 .labelText,#mermaid-1605862734911 .labelText &gt; tspan{fill:#333;stroke:none;}#mermaid-1605862734911 .loopText,#mermaid-1605862734911 .loopText &gt; tspan{fill:#333;stroke:none;}#mermaid-1605862734911 .loopLine{stroke-width:2px;stroke-dasharray:2,2;stroke:hsl(0,0%,83%);fill:hsl(0,0%,83%);}#mermaid-1605862734911 .note{stroke:hsl(60,100%,23.3333333333%);fill:#ffa;}#mermaid-1605862734911 .noteText,#mermaid-1605862734911 .noteText &gt; tspan{fill:#333;stroke:none;}#mermaid-1605862734911 .activation0{fill:#f4f4f4;stroke:#666;}#mermaid-1605862734911 .activation1{fill:#f4f4f4;stroke:#666;}#mermaid-1605862734911 .activation2{fill:#f4f4f4;stroke:#666;}#mermaid-1605862734911:root{--mermaid-font-family:"trebuchet ms",verdana,arial;}#mermaid-1605862734911 sequence{fill:apa;}</style><g></g><g><line id="actor1524" x1="75" y1="5" x2="75" y2="390" class="actor-line" stroke-width="0.5px" stroke="#999"></line><rect x="0" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3" class="actor"></rect><text x="75" y="32.5" dominant-baseline="central" alignment-baseline="central" class="actor" style="text-anchor: middle; font-size: 14px; font-weight: 400; font-family: Open-Sans, sans-serif;"><tspan x="75" dy="0">Supabase</tspan></text></g><g><line id="actor1525" x1="490" y1="5" x2="490" y2="390" class="actor-line" stroke-width="0.5px" stroke="#999"></line><rect x="415" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3" class="actor"></rect><text x="490" y="32.5" dominant-baseline="central" alignment-baseline="central" class="actor" style="text-anchor: middle; font-size: 14px; font-weight: 400; font-family: Open-Sans, sans-serif;"><tspan x="490" dy="0">Vercel</tspan></text></g><g><line id="actor1526" x1="871" y1="5" x2="871" y2="390" class="actor-line" stroke-width="0.5px" stroke="#999"></line><rect x="796" y="0" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3" class="actor"></rect><text x="871" y="32.5" dominant-baseline="central" alignment-baseline="central" class="actor" style="text-anchor: middle; font-size: 14px; font-weight: 400; font-family: Open-Sans, sans-serif;"><tspan x="871" dy="0">Stripe</tspan></text></g><defs><marker id="arrowhead" refX="5" refY="2" markerWidth="6" markerHeight="4" orient="auto"><path d="M 0,0 V 4 L6,2 Z"></path></marker></defs><defs><marker id="crosshead" markerWidth="15" markerHeight="8" orient="auto" refX="16" refY="4"><path fill="black" stroke="#000000" stroke-width="1px" d="M 9,2 V 6 L16,4 Z" style="stroke-dasharray: 0, 0;"></path><path fill="none" stroke="#000000" stroke-width="1px" d="M 0,1 L 6,7 M 6,1 L 0,7" style="stroke-dasharray: 0, 0;"></path></marker></defs><defs><marker id="sequencenumber" refX="15" refY="15" markerWidth="60" markerHeight="40" orient="auto"><circle cx="15" cy="15" r="6"></circle></marker></defs><text x="283" y="80" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">Authentication: Sign Up/In</text><line x1="75" y1="113" x2="490" y2="113" class="messageLine0" stroke-width="2" stroke="none" style="fill: none;"></line><text x="283" y="128" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">Database API: CRUD</text><line x1="75" y1="161" x2="490" y2="161" class="messageLine0" stroke-width="2" stroke="none" style="fill: none;"></line><text x="681" y="176" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">API route: Create Checkout/Portal Sessions</text><line x1="490" y1="209" x2="871" y2="209" class="messageLine0" stroke-width="2" stroke="none" style="fill: none;"></line><text x="681" y="224" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">Webhook: Products/Prices/Subscriptions</text><line x1="871" y1="257" x2="490" y2="257" class="messageLine0" stroke-width="2" stroke="none" marker-end="url(#arrowhead)" style="fill: none;"></line><text x="283" y="272" text-anchor="middle" dominant-baseline="middle" alignment-baseline="middle" class="messageText" dy="1em" style="font-family: &quot;trebuchet ms&quot;, verdana, arial; font-size: 16px; font-weight: 400;">API route: Upsert Products/Prices/Subscriptions</text><line x1="490" y1="305" x2="75" y2="305" class="messageLine0" stroke-width="2" stroke="none" marker-end="url(#arrowhead)" style="fill: none;"></line><g><rect x="0" y="325" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3" class="actor"></rect><text x="75" y="357.5" dominant-baseline="central" alignment-baseline="central" class="actor" style="text-anchor: middle; font-size: 14px; font-weight: 400; font-family: Open-Sans, sans-serif;"><tspan x="75" dy="0">Supabase</tspan></text></g><g><rect x="415" y="325" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3" class="actor"></rect><text x="490" y="357.5" dominant-baseline="central" alignment-baseline="central" class="actor" style="text-anchor: middle; font-size: 14px; font-weight: 400; font-family: Open-Sans, sans-serif;"><tspan x="490" dy="0">Vercel</tspan></text></g><g><rect x="796" y="325" fill="#eaeaea" stroke="#666" width="150" height="65" rx="3" ry="3" class="actor"></rect><text x="871" y="357.5" dominant-baseline="central" alignment-baseline="central" class="actor" style="text-anchor: middle; font-size: 14px; font-weight: 400; font-family: Open-Sans, sans-serif;"><tspan x="871" dy="0">Stripe</tspan></text></g></svg>
